(()=>{var a={};a.id=3302,a.ids=[3302],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:a=>{"use strict";a.exports=require("node:buffer")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31316:(a,b,c)=>{"use strict";c.d(b,{a:()=>n,j:()=>m});var d=c(18529),e=c(12306),f=c(96330);let g=null,h=!process.env.VERCEL&&!process.env.RAILWAY_ENVIRONMENT,i=process.env.DATABASE_URL?.includes("build-mock-host")||process.env.DATABASE_URL?.includes("localhost"),j=process.env.DATABASE_URL&&process.env.DATABASE_URL.startsWith("postgresql://")&&!i;if(!h&&j)try{g=new f.PrismaClient}catch(a){console.warn("Failed to create Prisma client:",a)}let k=null;function l(){if(!k){let a={secret:process.env.BETTER_AUTH_SECRET||"default-secret-change-in-production",baseURL:"https://onlyrules.codes",trustedOrigins:["http://localhost:3000","https://onlyrules.codes"],emailAndPassword:{enabled:!1},session:{expiresIn:604800,updateAge:86400},socialProviders:{...process.env.GITHUB_CLIENT_ID&&process.env.GITHUB_CLIENT_SECRET?{github:{clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET,scope:["read:user","user:email"]}}:{}},callbacks:{async signIn({user:a,account:b,profile:c}){if(!a.email&&b?.provider==="github")if(console.log("GitHub user without email:",{user:a,profile:c}),c&&c.email)a.email=c.email;else{let b=a.name?.toLowerCase().replace(/[^a-z0-9]/g,".")||"user";a.email=`${b}.${Date.now()}@github.user`,console.log("Created placeholder email:",a.email)}return!0}}};g&&(a.database=(0,e._)(g,{provider:"postgresql"})),k=(0,d.li)(a)}return k}let m=new Proxy({},{get:(a,b)=>l()[b]}),n=()=>l()},36523:(a,b,c)=>{"use strict";Object.defineProperty(b,"I",{enumerable:!0,get:function(){return g}});let d=c(89843),e=c(72962),f=c(45393);async function g(a,b,c,g){if((0,d.isNodeNextResponse)(b)){var h;b.statusCode=c.status,b.statusMessage=c.statusText;let d=["set-cookie","www-authenticate","proxy-authenticate","vary"];null==(h=c.headers)||h.forEach((a,c)=>{if("x-middleware-set-cookie"!==c.toLowerCase())if("set-cookie"===c.toLowerCase())for(let d of(0,f.splitCookiesString)(a))b.appendHeader(c,d);else{let e=void 0!==b.getHeader(c);(d.includes(c.toLowerCase())||!e)&&b.appendHeader(c,a)}});let{originalResponse:i}=b;c.body&&"HEAD"!==a.method?await (0,e.pipeToNodeResponse)(c.body,i,g):i.end()}}},37067:a=>{"use strict";a.exports=require("node:http")},44708:a=>{"use strict";a.exports=require("node:https")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48106:(a,b,c)=>{"use strict";a.exports=c(44870)},57975:a=>{"use strict";a.exports=require("node:util")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69332:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>P,patchFetch:()=>O,routeModule:()=>K,serverHooks:()=>N,workAsyncStorage:()=>L,workUnitAsyncStorage:()=>M});var d={};c.r(d),c.d(d,{GET:()=>I,POST:()=>J,dynamic:()=>G});var e=c(48106),f=c(48819),g=c(12050),h=c(88996),i=c(98730),j=c(261),k=c(36748),l=c(27462),m=c(16318),n=c(93949),o=c(36523),p=c(45393),q=c(41671),r=c(66704),s=c(86439),t=c(65262),u=c(31316);c(49442),c(51875),c(62055),c(61229),c(33746),c(30060);var v=c(75083);c(32395);var w=c(16766),x=c(95792),y=c(16741);c(39773),c(12486),c(84941),c(30377),(0,v.i)(async()=>({})),(0,v.i)({use:[v.l]},async a=>({session:a.context.session}));let z=w.Yj(),A=w.k5(["pending","accepted","rejected","canceled"]).default("pending");w.Ik({id:w.Yj().default(y.g),name:w.Yj(),slug:w.Yj(),logo:w.Yj().nullish().optional(),metadata:w.g1(w.Yj(),w.L5()).or(w.Yj().transform(a=>JSON.parse(a))).optional(),createdAt:w.p6()}),w.Ik({id:w.Yj().default(y.g),organizationId:w.Yj(),userId:x.Yj(),role:z,createdAt:w.p6().default(()=>new Date)}),w.Ik({id:w.Yj().default(y.g),organizationId:w.Yj(),email:w.Yj(),role:z,status:A,teamId:w.Yj().optional(),inviterId:w.Yj(),expiresAt:w.p6()}),w.Ik({id:w.Yj().default(y.g),name:w.Yj().min(1),organizationId:w.Yj(),createdAt:w.p6(),updatedAt:w.p6().optional()}),w.Ik({id:w.Yj().default(y.g),teamId:w.Yj(),userId:w.Yj(),createdAt:w.p6().default(()=>new Date)});let B=["admin","member","owner"];w.KC([w.k5(B),w.YO(w.k5(B))]);var C=c(10257);function D(a){return{newRole:a=>{var b;return{authorize(a,c="AND"){let d=!1;for(let[e,f]of Object.entries(a)){let a=b[e];if(!a)return{success:!1,error:`You are not allowed to access resource: ${e}`};if(Array.isArray(f))d=f.every(b=>a.includes(b));else if("object"==typeof f)d="OR"===f.connector?f.actions.some(b=>a.includes(b)):f.actions.every(b=>a.includes(b));else throw new C.B("Invalid access control request");if(d&&"OR"===c)return{success:d};if(!d&&"AND"===c)return{success:!1,error:`unauthorized to access resource "${e}"`}}return d?{success:d}:{success:!1,error:"Not authorized"}},statements:b=a}},statements:a}}let E=D({organization:["update","delete"],member:["create","update","delete"],invitation:["create","cancel"],team:["create","update","delete"]});E.newRole({organization:["update"],invitation:["create","cancel"],member:["create","update","delete"],team:["create","update","delete"]}),E.newRole({organization:["update","delete"],member:["create","update","delete"],invitation:["create","cancel"],team:["create","update","delete"]}),E.newRole({organization:[],member:[],invitation:[],team:[]});let F=D({user:["create","list","set-role","ban","impersonate","delete","set-password","update"],session:["list","revoke","delete"]});F.newRole({user:["create","list","set-role","ban","impersonate","delete","set-password","update"],session:["list","revoke","delete"]}),F.newRole({user:[],session:[]}),c(55094),w.Ik({id:w.Yj(),publicKey:w.Yj(),privateKey:w.Yj(),createdAt:w.p6()}),c(98196),c(7740);let G="force-dynamic",H=function(a){let b=async b=>"handler"in a?a.handler(b):a(b);return{GET:b,POST:b}}((0,u.a)());async function I(a){return H.GET(a)}async function J(a){return H.POST(a)}let K=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/auth/[...all]/route",pathname:"/api/auth/[...all]",filename:"route",bundlePath:"app/api/auth/[...all]/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/api/auth/[...all]/route.ts",nextConfigOutput:"standalone",userland:d}),{workAsyncStorage:L,workUnitAsyncStorage:M,serverHooks:N}=K;function O(){return(0,g.patchFetch)({workAsyncStorage:L,workUnitAsyncStorage:M})}async function P(a,b,c){var d;let e="/api/auth/[...all]/route";"/index"===e&&(e="/");let g=await K.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=g,D=(0,j.normalizeAppPath)(e),E=!!(y.dynamicRoutes[D]||y.routes[C]);if(E&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let F=null;!E||K.isDev||x||(F="/index"===(F=C)?"/":F);let G=!0===K.isDev||!E,H=E&&!G,I=a.method||"GET",J=(0,i.getTracer)(),L=J.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:G,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:H,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>K.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=J.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${I} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${I} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!E)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await K.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})},z),b}},l=await K.handleResponse({req:a,nextConfig:w,cacheKey:F,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!E)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&E||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await J.withPropagatedContext(a.headers,()=>J.trace(m.BaseServerSpan.handleRequest,{spanName:`${I} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":I,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:H,isOnDemandRevalidate:A})}),E)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},77598:a=>{"use strict";a.exports=require("node:crypto")},78474:a=>{"use strict";a.exports=require("node:events")},80408:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87032:()=>{},96330:a=>{"use strict";a.exports=require("@prisma/client")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[657,3608],()=>b(b.s=69332));module.exports=c})();