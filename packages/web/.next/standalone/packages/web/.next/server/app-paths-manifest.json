{"/_not-found/page": "app/_not-found/page.js", "/api/search/route": "app/api/search/route.js", "/api/robots.txt/route": "app/api/robots.txt/route.js", "/api/auth/[...all]/route": "app/api/auth/[...all]/route.js", "/api/rulesets/[id]/route": "app/api/rulesets/[id]/route.js", "/api/rules/raw/route": "app/api/rules/raw/route.js", "/api/rules/[id]/route": "app/api/rules/[id]/route.js", "/api/rules/export/route": "app/api/rules/export/route.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/api/rulesets/route": "app/api/rulesets/route.js", "/api/tags/route": "app/api/tags/route.js", "/api/sitemap.xml/route": "app/api/sitemap.xml/route.js", "/api/rules/download/route": "app/api/rules/download/route.js", "/api/rules/route": "app/api/rules/route.js", "/(main)/r/[id]/page": "app/(main)/r/[id]/page.js", "/(main)/test-theme/page": "app/(main)/test-theme/page.js", "/(main)/test-integration/page": "app/(main)/test-integration/page.js", "/(main)/auth/signin/page": "app/(main)/auth/signin/page.js", "/(main)/auth/signup/page": "app/(main)/auth/signup/page.js", "/(main)/profile/[username]/page": "app/(main)/profile/[username]/page.js", "/(main)/rulesets/page": "app/(main)/rulesets/page.js", "/(main)/rulesets/[id]/page": "app/(main)/rulesets/[id]/page.js", "/(main)/dashboard/page": "app/(main)/dashboard/page.js", "/(main)/tutorials/page": "app/(main)/tutorials/page.js", "/(main)/rules/[id]/page": "app/(main)/rules/[id]/page.js", "/(main)/templates/page": "app/(main)/templates/page.js", "/(docs)/docs/[[...slug]]/page": "app/(docs)/docs/[[...slug]]/page.js", "/(main)/ides/github-copilot/page": "app/(main)/ides/github-copilot/page.js", "/(main)/ides/page": "app/(main)/ides/page.js", "/(main)/ides/cline/page": "app/(main)/ides/cline/page.js", "/(main)/ides/claude/page": "app/(main)/ides/claude/page.js", "/(main)/ides/windsurf/page": "app/(main)/ides/windsurf/page.js", "/(main)/ides/cursor/page": "app/(main)/ides/cursor/page.js", "/(main)/ides/augment/page": "app/(main)/ides/augment/page.js", "/(main)/(static)/privacy/page": "app/(main)/(static)/privacy/page.js", "/(main)/(static)/page": "app/(main)/(static)/page.js", "/(main)/(static)/terms/page": "app/(main)/(static)/terms/page.js"}