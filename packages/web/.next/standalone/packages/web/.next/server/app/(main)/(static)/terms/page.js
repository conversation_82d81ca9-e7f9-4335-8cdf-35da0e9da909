(()=>{var a={};a.id=4997,a.ids=[4997],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5536:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,metadata:()=>e});var d=c(38828);let e={title:"Terms of Service - OnlyRules",description:"Terms of Service for OnlyRules - AI Prompt Management Platform",robots:"index, follow"};function f(){return(0,d.jsx)("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:(0,d.jsxs)("div",{className:"prose prose-gray dark:prose-invert max-w-none",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Terms of Service"}),(0,d.jsxs)("p",{className:"text-muted-foreground mb-6",children:[(0,d.jsx)("strong",{children:"Last updated:"})," ",new Date().toLocaleDateString()]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"1. Acceptance of Terms"}),(0,d.jsx)("p",{children:'By accessing and using OnlyRules ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. These Terms of Service ("Terms") govern your use of the OnlyRules website and platform.'})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"2. Description of Service"}),(0,d.jsx)("p",{children:"OnlyRules is an AI prompt management platform that allows users to create, organize, and share AI prompt rules for various IDEs and coding assistants. Our service includes:"}),(0,d.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,d.jsx)("li",{children:"AI prompt rule creation and management tools"}),(0,d.jsx)("li",{children:"Community-driven template sharing"}),(0,d.jsx)("li",{children:"Integration with popular IDEs and AI coding assistants"}),(0,d.jsx)("li",{children:"Documentation and tutorials"})]})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"3. User Accounts"}),(0,d.jsx)("p",{children:"To access certain features of the Service, you may be required to create an account. You are responsible for:"}),(0,d.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,d.jsx)("li",{children:"Maintaining the confidentiality of your account credentials"}),(0,d.jsx)("li",{children:"All activities that occur under your account"}),(0,d.jsx)("li",{children:"Providing accurate and complete information"}),(0,d.jsx)("li",{children:"Promptly updating your account information"})]})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"4. User Content and Conduct"}),(0,d.jsx)("p",{children:"By using our Service, you agree to:"}),(0,d.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,d.jsx)("li",{children:"Not upload, post, or transmit any content that is illegal, harmful, or violates intellectual property rights"}),(0,d.jsx)("li",{children:"Not use the Service for any unlawful purpose or to violate any laws"}),(0,d.jsx)("li",{children:"Not interfere with or disrupt the Service or servers"}),(0,d.jsx)("li",{children:"Respect the rights and privacy of other users"}),(0,d.jsx)("li",{children:"Not engage in any form of harassment, abuse, or spam"})]})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"5. Intellectual Property Rights"}),(0,d.jsx)("p",{children:"The Service and its original content, features, and functionality are owned by OnlyRules and are protected by international copyright, trademark, patent, trade secret, and other intellectual property laws."}),(0,d.jsx)("p",{className:"mt-4",children:"You retain ownership of content you create and share through the Service, but grant us a license to use, display, and distribute such content in connection with the Service."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"6. Privacy Policy"}),(0,d.jsx)("p",{children:"Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, to understand our practices."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"7. Third-Party Services"}),(0,d.jsx)("p",{children:"Our Service may contain links to third-party websites or services that are not owned or controlled by OnlyRules. We have no control over and assume no responsibility for the content, privacy policies, or practices of any third-party services."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"8. Advertising"}),(0,d.jsx)("p",{children:"The Service may display advertisements and other content from third parties. We are not responsible for the availability, accuracy, or content of such advertisements."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"9. Disclaimer of Warranties"}),(0,d.jsx)("p",{children:'The Service is provided on an "AS IS" and "AS AVAILABLE" basis. OnlyRules expressly disclaims all warranties of any kind, whether express or implied, including but not limited to the implied warranties of merchantability, fitness for a particular purpose, and non-infringement.'})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"10. Limitation of Liability"}),(0,d.jsx)("p",{children:"In no event shall OnlyRules, its directors, employees, partners, agents, suppliers, or affiliates be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"11. Termination"}),(0,d.jsx)("p",{children:"We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever, including without limitation if you breach the Terms."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"12. Governing Law"}),(0,d.jsx)("p",{children:"These Terms shall be interpreted and governed by the laws of the jurisdiction in which OnlyRules operates, without regard to its conflict of law provisions."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"13. Changes to Terms"}),(0,d.jsx)("p",{children:"We reserve the right to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"14. Contact Information"}),(0,d.jsx)("p",{children:"If you have any questions about these Terms of Service, please contact us through our website or support channels."})]})]})})}},7878:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>B.default,__next_app__:()=>H,handler:()=>J,pages:()=>G,routeModule:()=>I,tree:()=>F});var d=c(24332),e=c(48819),f=c(93949),g=c(98730),h=c(88996),i=c(16318),j=c(3093),k=c(36748),l=c(98190),m=c(53904),n=c(47735),o=c(20611),p=c(22512),q=c(261),r=c(13863),s=c(8748),t=c(26713),u=c(65262),v=c(97779),w=c(5303),x=c(66704),y=c(67656),z=c(3072),A=c(86439),B=c(866),C=c(97540),D=c(49005),E={};for(let a in C)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(E[a]=()=>C[a]);c.d(b,E);let F={children:["",{children:["(main)",{children:["(static)",{children:["terms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,5536)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/(static)/terms/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,91491)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/(static)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,17217)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,33409)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,92179)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{"global-error":[()=>Promise.resolve().then(c.bind(c,866)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,866)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,50134,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,G=["/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/(static)/terms/page.tsx"],H={require:c,loadChunk:()=>Promise.resolve()},I=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(main)/(static)/terms/page",pathname:"/terms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:F},distDir:".next",projectDir:""});async function J(a,b,c){var d;let E="/(main)/(static)/terms/page";"/index"===E&&(E="/");let K="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await I.prepare(a,b,{srcPage:E,multiZoneDraftMode:K});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(E),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===I.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&I.isDev&&(az=_);let aA={...C,tree:F,pages:G,GlobalError:B.default,handler:J,routeModule:I,__next_app__:H};W&&X&&(0,n.setReferenceManifestsSingleton)({page:E,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return I.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:I,page:E,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:I.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:K,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>I.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:I.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!I.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===I.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await I.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await I.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),I.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!I.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&D.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await I.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21047:(a,b,c)=>{Promise.resolve().then(c.bind(c,56909)),Promise.resolve().then(c.bind(c,44576)),Promise.resolve().then(c.bind(c,23992)),Promise.resolve().then(c.bind(c,8956)),Promise.resolve().then(c.bind(c,58407)),Promise.resolve().then(c.bind(c,81160)),Promise.resolve().then(c.bind(c,21997)),Promise.resolve().then(c.bind(c,88756)),Promise.resolve().then(c.bind(c,34395)),Promise.resolve().then(c.bind(c,21234)),Promise.resolve().then(c.bind(c,3553)),Promise.resolve().then(c.bind(c,68239)),Promise.resolve().then(c.bind(c,99345)),Promise.resolve().then(c.bind(c,5290)),Promise.resolve().then(c.bind(c,71746)),Promise.resolve().then(c.bind(c,37934)),Promise.resolve().then(c.bind(c,43183)),Promise.resolve().then(c.bind(c,97242)),Promise.resolve().then(c.bind(c,11814)),Promise.resolve().then(c.bind(c,79282)),Promise.resolve().then(c.bind(c,14577)),Promise.resolve().then(c.bind(c,62576)),Promise.resolve().then(c.bind(c,90805)),Promise.resolve().then(c.bind(c,80353)),Promise.resolve().then(c.bind(c,61684)),Promise.resolve().then(c.bind(c,37503)),Promise.resolve().then(c.bind(c,63321)),Promise.resolve().then(c.bind(c,3004)),Promise.resolve().then(c.bind(c,31844)),Promise.resolve().then(c.bind(c,42782)),Promise.resolve().then(c.bind(c,83631)),Promise.resolve().then(c.bind(c,5592)),Promise.resolve().then(c.bind(c,39620)),Promise.resolve().then(c.bind(c,42438)),Promise.resolve().then(c.bind(c,61090)),Promise.resolve().then(c.bind(c,45732)),Promise.resolve().then(c.bind(c,6314)),Promise.resolve().then(c.bind(c,16152)),Promise.resolve().then(c.bind(c,21411)),Promise.resolve().then(c.bind(c,30435)),Promise.resolve().then(c.bind(c,31173)),Promise.resolve().then(c.bind(c,17017)),Promise.resolve().then(c.bind(c,98498))},21971:()=>{},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},49071:(a,b,c)=>{Promise.resolve().then(c.bind(c,84823)),Promise.resolve().then(c.bind(c,81506)),Promise.resolve().then(c.bind(c,3902)),Promise.resolve().then(c.bind(c,72294)),Promise.resolve().then(c.bind(c,7377)),Promise.resolve().then(c.bind(c,62062)),Promise.resolve().then(c.bind(c,58467)),Promise.resolve().then(c.bind(c,92790)),Promise.resolve().then(c.bind(c,62973)),Promise.resolve().then(c.bind(c,96060)),Promise.resolve().then(c.bind(c,44207)),Promise.resolve().then(c.bind(c,24029)),Promise.resolve().then(c.bind(c,16627)),Promise.resolve().then(c.bind(c,71988)),Promise.resolve().then(c.bind(c,58632)),Promise.resolve().then(c.bind(c,22584)),Promise.resolve().then(c.bind(c,20829)),Promise.resolve().then(c.bind(c,4344)),Promise.resolve().then(c.bind(c,26436)),Promise.resolve().then(c.bind(c,82e3)),Promise.resolve().then(c.bind(c,91159)),Promise.resolve().then(c.bind(c,89702)),Promise.resolve().then(c.bind(c,77271)),Promise.resolve().then(c.bind(c,99087)),Promise.resolve().then(c.bind(c,64718)),Promise.resolve().then(c.bind(c,89725)),Promise.resolve().then(c.bind(c,20539)),Promise.resolve().then(c.bind(c,14108)),Promise.resolve().then(c.bind(c,3142)),Promise.resolve().then(c.bind(c,75370)),Promise.resolve().then(c.bind(c,92499)),Promise.resolve().then(c.bind(c,49802)),Promise.resolve().then(c.bind(c,92334)),Promise.resolve().then(c.bind(c,95424)),Promise.resolve().then(c.bind(c,48280)),Promise.resolve().then(c.bind(c,93374)),Promise.resolve().then(c.bind(c,36097)),Promise.resolve().then(c.bind(c,62021)),Promise.resolve().then(c.bind(c,74325)),Promise.resolve().then(c.bind(c,14788)),Promise.resolve().then(c.bind(c,16996)),Promise.resolve().then(c.bind(c,45649)),Promise.resolve().then(c.bind(c,5924))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80408:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87032:()=>{},91491:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,dynamic:()=>f,metadata:()=>g});var d=c(38828);c(21971);var e=c(33138);let f="force-dynamic",g={title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.",keywords:"AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, Junie, Trae, Lingma, Kiro, Tencent Cloud CodeBuddy",authors:[{name:"OnlyRules Team"}],openGraph:{title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs.",type:"website"}};function h({children:a}){return(0,d.jsx)("div",{className:"dark",children:(0,d.jsx)(e.a,{style:{minHeight:"100vh"},className:"bg-background",children:(0,d.jsx)(e.a,{children:a})})})}}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[657,5208,1770,9119,1985,3931],()=>b(b.s=7878));module.exports=c})();