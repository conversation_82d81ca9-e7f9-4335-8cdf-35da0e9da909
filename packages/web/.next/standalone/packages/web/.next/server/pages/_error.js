"use strict";(()=>{var a={};a.id=2731,a.ids=[2731,3220],a.modules={8732:a=>{a.exports=require("react/jsx-runtime")},15952:a=>{a.exports=require("next/dist/shared/lib/router/utils/remove-trailing-slash")},23103:a=>{a.exports=require("next/dist/shared/lib/router/utils/add-path-prefix")},26713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28965:(a,b,c)=>{c.r(b),c.d(b,{config:()=>M,default:()=>I,getServerSideProps:()=>L,getStaticPaths:()=>K,getStaticProps:()=>J,handler:()=>U,reportWebVitals:()=>N,routeModule:()=>T,unstable_getServerProps:()=>R,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>Q,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>O});var d={};c.r(d),c.d(d,{default:()=>v});var e=c(62636),f=c(44850),g=c(40465),h=c(78413),i=c(41322),j=c(41227),k=c(16208),l=c(77400),m=c(57909),n=c(82753),o=c(83410),p=c(37496),q=c.n(p),r=c(52440),s=c.n(r),t=c(8732);function u({statusCode:a}){return(0,t.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",fontFamily:"system-ui, sans-serif",textAlign:"center",padding:"20px"},children:[(0,t.jsx)("h1",{style:{fontSize:"4rem",margin:"0",color:"#333"},children:a||"Error"}),(0,t.jsx)("p",{style:{fontSize:"1.2rem",color:"#666",marginTop:"1rem"},children:404===a?"This page could not be found.":"An error occurred on the server."}),(0,t.jsx)("a",{href:"/",style:{marginTop:"2rem",padding:"10px 20px",backgroundColor:"#0070f3",color:"white",textDecoration:"none",borderRadius:"5px"},children:"Go Home"})]})}u.getInitialProps=({res:a,err:b})=>({statusCode:a?a.statusCode:b?b.statusCode:404});let v=u;var w=c(82246),x=c(80156),y=c(77471),z=c(19559),A=c(80727),B=c(94774),C=c(25686),D=c(86439),E=c(32143),F=c(26713),G=c(23103),H=c(15952);let I=(0,o.M)(d,"default"),J=(0,o.M)(d,"getStaticProps"),K=(0,o.M)(d,"getStaticPaths"),L=(0,o.M)(d,"getServerSideProps"),M=(0,o.M)(d,"config"),N=(0,o.M)(d,"reportWebVitals"),O=(0,o.M)(d,"unstable_getStaticProps"),P=(0,o.M)(d,"unstable_getStaticPaths"),Q=(0,o.M)(d,"unstable_getStaticParams"),R=(0,o.M)(d,"unstable_getServerProps"),S=(0,o.M)(d,"unstable_getServerSideProps"),T=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},distDir:".next",projectDir:"",components:{App:s(),Document:q()},userland:d});async function U(a,b,c){var e,o;let p="/_error";"/index"===p&&(p="/");let q="false",r=await T.prepare(a,b,{srcPage:p,multiZoneDraftMode:q});if(!r){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{buildId:s,query:t,params:u,parsedUrl:I,originalQuery:N,originalPathname:O,buildManifest:P,nextFontManifest:Q,serverFilesManifest:R,reactLoadableManifest:S,prerenderManifest:U,isDraftMode:V,isOnDemandRevalidate:W,revalidateOnlyGenerated:X,locale:Y,locales:Z,defaultLocale:$,routerServerContext:_,nextConfig:aa,resolvedPathname:ab}=r,ac=null==R||null==(o=R.config)||null==(e=o.experimental)?void 0:e.isExperimentalCompile,ad=!!L,ae=!!J,af=!!K,ag=!!(v||d).getInitialProps,ah=t.amp&&M.amp,ai=null,aj=!1,ak=r.isNextDataRequest&&(ae||ad),al="/404"===p,am="/500"===p,an="/_error"===p;if(T.isDev||V||!ae||(ai=`${Y?`/${Y}`:""}${("/"===p||"/"===ab)&&Y?"":ab}${ah?".amp":""}`,(al||am||an)&&(ai=`${Y?`/${Y}`:""}${p}${ah?".amp":""}`),ai="/index"===ai?"/":ai),af&&!V){let a=(0,H.removeTrailingSlash)(Y?(0,G.addPathPrefix)(ab,`/${Y}`):ab),b=!!U.routes[a]||U.notFoundRoutes.includes(a),c=U.dynamicRoutes[p];if(c){if(!1===c.fallback&&!b)throw new D.NoFallbackError;"string"!=typeof c.fallback||b||ak||(aj=!0)}}(aj&&(0,F.isBot)(a.headers["user-agent"]||"")||(0,j.getRequestMeta)(a,"minimalMode"))&&(aj=!1);let ao=(0,h.getTracer)(),ap=ao.getActiveScopeSpan();try{let e=a.method||"GET",o=(0,i.formatUrl)({pathname:aa.trailingSlash?I.pathname:(0,H.removeTrailingSlash)(I.pathname||"/"),query:ae?{}:N}),r=(null==_?void 0:_.publicRuntimeConfig)||aa.publicRuntimeConfig,v=async h=>{var v,D;let F,G=async({previousCacheEntry:v})=>{var w;let x=async()=>{try{var c,f,w;return await T.render(a,b,{query:ae&&!ac?{...u,...ah?{amp:t.amp}:{}}:{...t,...u},params:u,page:p,renderContext:{isDraftMode:V,isFallback:aj,developmentNotFoundSourcePage:(0,j.getRequestMeta)(a,"developmentNotFoundSourcePage")},sharedContext:{buildId:s,customServer:!!(null==_?void 0:_.isCustomServer)||void 0,deploymentId:!1},renderOpts:{params:u,routeModule:T,page:p,pageConfig:M||{},Component:(0,k.T)(d),ComponentMod:d,getStaticProps:J,getStaticPaths:K,getServerSideProps:L,supportsDynamicResponse:!ae,buildManifest:P,nextFontManifest:Q,reactLoadableManifest:S,assetPrefix:aa.assetPrefix,strictNextHead:aa.experimental.strictNextHead??!0,previewProps:U.preview,images:aa.images,nextConfigOutput:aa.output,optimizeCss:!!aa.experimental.optimizeCss,nextScriptWorkers:!!aa.experimental.nextScriptWorkers,domainLocales:null==(c=aa.i18n)?void 0:c.domains,crossOrigin:aa.crossOrigin,multiZoneDraftMode:q,basePath:aa.basePath,canonicalBase:aa.amp.canonicalBase||"",ampOptimizerConfig:null==(f=aa.experimental.amp)?void 0:f.optimizer,disableOptimizedLoading:aa.experimental.disableOptimizedLoading,largePageDataBytes:aa.experimental.largePageDataBytes,runtimeConfig:Object.keys(r).length>0?r:void 0,isExperimentalCompile:ac,experimental:{clientTraceMetadata:aa.experimental.clientTraceMetadata||[]},locale:Y,locales:Z,defaultLocale:$,setIsrStatus:null==_?void 0:_.setIsrStatus,isNextDataRequest:ak&&(ad||ae),resolvedUrl:o,resolvedAsPath:ad||ag?(0,i.formatUrl)({pathname:ak?(0,m.normalizeDataPath)(O):O,query:N}):o,isOnDemandRevalidate:W,ErrorDebug:(0,j.getRequestMeta)(a,"PagesErrorDebug"),err:(0,j.getRequestMeta)(a,"invokeError"),dev:T.isDev,distDir:`${T.projectDir}/${T.distDir}`,ampSkipValidation:null==(w=aa.experimental.amp)?void 0:w.skipValidation,ampValidator:(0,j.getRequestMeta)(a,"ampValidator")}}).then(a=>{let{metadata:b}=a,c=b.cacheControl;return"isNotFound"in b&&b.isNotFound?{value:null,cacheControl:c}:b.isRedirect?{value:{kind:n.CachedRouteKind.REDIRECT,props:b.pageData??b.flightData},cacheControl:c}:{value:{kind:n.CachedRouteKind.PAGES,html:a,pageData:a.metadata.pageData,headers:a.metadata.headers,status:a.metadata.statusCode},cacheControl:c}}).finally(()=>{if(!h)return;h.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let c=ao.getRootSpanAttributes();if(!c)return;if(c.get("next.span_type")!==g.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${c.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let d=c.get("next.route");if(d){let a=`${e} ${d}`;h.setAttributes({"next.route":d,"http.route":d,"next.span_name":a}),h.updateName(a)}else h.updateName(`${e} ${a.url}`)})}catch(b){throw(null==v?void 0:v.isStale)&&await T.onRequestError(a,b,{routerKind:"Pages Router",routePath:p,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:W})},_),b}};if(v&&(aj=!1),aj){let b=await T.getResponseCache(a).get(T.isDev?null:Y?`/${Y}${p}`:p,async({previousCacheEntry:a=null})=>T.isDev?x():(0,C.toResponseCacheEntry)(a),{routeKind:f.RouteKind.PAGES,isFallback:!0,isRoutePPREnabled:!1,isOnDemandRevalidate:!1,incrementalCache:await T.getIncrementalCache(a,aa,U),waitUntil:c.waitUntil});if(b)return delete b.cacheControl,b.isMiss=!0,b}return!(0,j.getRequestMeta)(a,"minimalMode")&&W&&X&&!v?(b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null):aj&&(null==v||null==(w=v.value)?void 0:w.kind)===n.CachedRouteKind.PAGES?{value:{kind:n.CachedRouteKind.PAGES,html:new B.default(Buffer.from(v.value.html),{contentType:"text/html;utf-8",metadata:{statusCode:v.value.status,headers:v.value.headers}}),pageData:{},status:v.value.status,headers:v.value.headers},cacheControl:{revalidate:0,expire:void 0}}:x()},H=await T.handleResponse({cacheKey:ai,req:a,nextConfig:aa,routeKind:f.RouteKind.PAGES,isOnDemandRevalidate:W,revalidateOnlyGenerated:X,waitUntil:c.waitUntil,responseGenerator:G,prerenderManifest:U});if(!aj||(null==H?void 0:H.isMiss)||(aj=!1),H){if(ae&&!(0,j.getRequestMeta)(a,"minimalMode")&&b.setHeader("x-nextjs-cache",W?"REVALIDATED":H.isMiss?"MISS":H.isStale?"STALE":"HIT"),!ae||aj)b.getHeader("Cache-Control")||(F={revalidate:0,expire:void 0});else if(al){let b=(0,j.getRequestMeta)(a,"notFoundRevalidate");F={revalidate:void 0===b?0:b,expire:void 0}}else if(am)F={revalidate:0,expire:void 0};else if(H.cacheControl)if("number"==typeof H.cacheControl.revalidate){if(H.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${H.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});F={revalidate:H.cacheControl.revalidate,expire:(null==(v=H.cacheControl)?void 0:v.expire)??aa.expireTime}}else F={revalidate:z.CACHE_ONE_YEAR,expire:void 0};if(F&&!b.getHeader("Cache-Control")&&b.setHeader("Cache-Control",(0,w.getCacheControlHeader)(F)),!H.value)return((0,j.addRequestMeta)(a,"notFoundRevalidate",null==(D=H.cacheControl)?void 0:D.revalidate),b.statusCode=404,ak)?void b.end('{"notFound":true}'):void((null==_?void 0:_.render404)?await _.render404(a,b,I,!1):b.end("This page could not be found"));if(H.value.kind===n.CachedRouteKind.REDIRECT)if(!ak)return await (a=>{let c={destination:a.pageProps.__N_REDIRECT,statusCode:a.pageProps.__N_REDIRECT_STATUS,basePath:a.pageProps.__N_REDIRECT_BASE_PATH},d=(0,y.getRedirectStatus)(c),{basePath:e}=aa;e&&!1!==c.basePath&&c.destination.startsWith("/")&&(c.destination=`${e}${c.destination}`),c.destination.startsWith("/")&&(c.destination=(0,x.normalizeRepeatedSlashes)(c.destination)),b.statusCode=d,b.setHeader("Location",c.destination),d===E.RedirectStatusCode.PermanentRedirect&&b.setHeader("Refresh",`0;url=${c.destination}`),b.end(c.destination)})(H.value.props),null;else{b.setHeader("content-type","application/json"),b.end(JSON.stringify(H.value.props));return}if(H.value.kind!==n.CachedRouteKind.PAGES)throw Object.defineProperty(Error("Invariant: received non-pages cache entry in pages handler"),"__NEXT_ERROR_CODE",{value:"E695",enumerable:!1,configurable:!0});if(T.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),V&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),(0,j.getRequestMeta)(a,"customErrorRender")||an&&(0,j.getRequestMeta)(a,"minimalMode")&&500===b.statusCode)return null;await (0,A.sendRenderResult)({req:a,res:b,result:!ak||an||am?H.value.html:new B.default(Buffer.from(JSON.stringify(H.value.pageData)),{contentType:"application/json",metadata:H.value.html.metadata}),generateEtags:aa.generateEtags,poweredByHeader:aa.poweredByHeader,cacheControl:T.isDev?void 0:F,type:ak?"json":"html"})}};ap?await v():await ao.withPropagatedContext(a.headers,()=>ao.trace(g.BaseServerSpan.handleRequest,{spanName:`${e} ${a.url}`,kind:h.SpanKind.SERVER,attributes:{"http.method":e,"http.target":a.url}},v))}catch(b){throw b instanceof D.NoFallbackError||await T.onRequestError(a,b,{routerKind:"Pages Router",routePath:p,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:W})},_),b}}},33873:a=>{a.exports=require("path")},40361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},41322:a=>{a.exports=require("next/dist/shared/lib/router/utils/format-url")},57909:a=>{a.exports=require("next/dist/shared/lib/page-path/normalize-data-path")},80156:a=>{a.exports=require("next/dist/shared/lib/utils")},82015:a=>{a.exports=require("react")},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[7496,7871],()=>b(b.s=28965));module.exports=c})();