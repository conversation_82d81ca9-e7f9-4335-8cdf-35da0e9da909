(()=>{var a={};a.id=1388,a.ids=[1388],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26316:(a,b,c)=>{"use strict";c.d(b,{z:()=>e});var d=c(96330);let e=globalThis.prisma??new d.PrismaClient},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80408:()=>{},81501:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>E,patchFetch:()=>D,routeModule:()=>z,serverHooks:()=>C,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>B});var d={};c.r(d),c.d(d,{GET:()=>x,dynamic:()=>w});var e=c(48106),f=c(48819),g=c(12050),h=c(88996),i=c(98730),j=c(261),k=c(36748),l=c(27462),m=c(16318),n=c(93949),o=c(36523),p=c(45393),q=c(41671),r=c(66704),s=c(86439),t=c(65262),u=c(4235),v=c(26316);let w="force-dynamic";async function x(a){if(!process.env.VERCEL&&!process.env.RAILWAY_ENVIRONMENT)return u.NextResponse.json({error:"Service temporarily unavailable"},{status:503});try{let{searchParams:b}=new URL(a.url),c=b.get("ids")?.split(",").filter(Boolean)||[],d=b.get("tags")?.split(",").filter(Boolean)||[],e=b.get("ideType"),f=b.get("search")||"",g={visibility:"PUBLIC"};c.length>0&&(g.id={in:c}),d.length>0&&(g.tags={some:{tag:{name:{in:d}}}}),e&&"ALL"!==e&&(g.ideType=e),f&&(g.OR=[{title:{contains:f,mode:"insensitive"}},{description:{contains:f,mode:"insensitive"}}]);let h=await v.z.rule.findMany({where:g,include:{tags:{include:{tag:!0}},user:{select:{name:!0}}},orderBy:{updatedAt:"desc"}});if(0===h.length)return u.NextResponse.json({error:"No rules found matching the criteria"},{status:404});let i=h.map(a=>({filename:`${y(a.title)}.mdx`,content:function(a){let b=a.tags.map(a=>a.tag.name).join(", "),c=a.user?.name||"Unknown",d=new Date(a.createdAt).toLocaleDateString(),e=new Date(a.updatedAt).toLocaleDateString();return`---
title: "${a.title}"
description: "${a.description||""}"
author: "${c}"
createdAt: "${d}"
updatedAt: "${e}"
ideType: "${a.ideType}"
visibility: "${a.visibility}"
tags: [${a.tags.map(a=>`"${a.tag.name}"`).join(", ")}]
---

# ${a.title}

${a.description?`> ${a.description}

`:""}

## Metadata

- **IDE Type:** ${a.ideType}
- **Author:** ${c}
- **Created:** ${d}
- **Updated:** ${e}
${b?`- **Tags:** ${b}`:""}

## Rule Content

${a.content}
`}(a)}));if(1===i.length)return new u.NextResponse(i[0].content,{headers:{"Content-Type":"text/plain; charset=utf-8","Content-Disposition":`attachment; filename="${i[0].filename}"`}});let j=i.map(a=>`<!-- File: ${a.filename} -->
${a.content}`).join("\n\n\x3c!-- ============================================ --\x3e\n\n"),k=function(a,b,c){let d=["rules"];return a&&d.push(y(a)),c&&"ALL"!==c&&d.push(c.toLowerCase()),b&&b.length>0&&d.push(b.slice(0,3).map(a=>y(a)).join("-")),d.push(new Date().toISOString().split("T")[0]),d.join("-")+".mdx"}(f||void 0,d,e||void 0);return new u.NextResponse(j,{headers:{"Content-Type":"text/plain; charset=utf-8","Content-Disposition":`attachment; filename="${k}"`}})}catch(a){return console.error("Error exporting rules:",a),u.NextResponse.json({error:"Failed to export rules"},{status:500})}}function y(a){return a.replace(/[^a-z0-9]/gi,"-").replace(/-+/g,"-").replace(/^-|-$/g,"").toLowerCase().substring(0,100)}let z=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/rules/export/route",pathname:"/api/rules/export",filename:"route",bundlePath:"app/api/rules/export/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/api/rules/export/route.ts",nextConfigOutput:"standalone",userland:d}),{workAsyncStorage:A,workUnitAsyncStorage:B,serverHooks:C}=z;function D(){return(0,g.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:B})}async function E(a,b,c){var d;let e="/api/rules/export/route";"/index"===e&&(e="/");let g=await z.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||z.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===z.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>z.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>z.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await z.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await z.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await z.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},83374:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{isRequestAPICallableInsideAfter:function(){return i},throwForSearchParamsAccessInUseCache:function(){return h},throwWithStaticGenerationBailoutError:function(){return f},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return g}});let d=c(10960),e=c(3295);function f(a,b){throw Object.defineProperty(new d.StaticGenBailoutError(`Route ${a} couldn't be rendered statically because it used ${b}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function g(a,b){throw Object.defineProperty(new d.StaticGenBailoutError(`Route ${a} with \`dynamic = "error"\` couldn't be rendered statically because it used ${b}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function h(a,b){let c=Object.defineProperty(Error(`Route ${a.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw Error.captureStackTrace(c,b),a.invalidDynamicUsageError??=c,c}function i(){let a=e.afterTaskAsyncStorage.getStore();return(null==a?void 0:a.rootTaskSpawnPhase)==="action"}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87032:()=>{},96330:a=>{"use strict";a.exports=require("@prisma/client")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[657,2626],()=>b(b.s=81501));module.exports=c})();