(()=>{var a={};a.id=6395,a.ids=[6395],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},866:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx","default")},1048:(a,b,c)=>{"use strict";c.d(b,{BaseLinkItem:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call BaseLinkItem() from the server but BaseLinkItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/links.js","BaseLinkItem")},1652:(a,b,c)=>{"use strict";c.d(b,{LargeSearchToggle:()=>k,SearchToggle:()=>j});var d=c(13486),e=c(19037),f=c(30186),g=c(9650),h=c(91518),i=c(93507);function j({hideIfDisabled:a,size:b="icon-sm",color:c="ghost",...g}){let{setOpenSearch:j,enabled:k}=(0,f.$A)();return a&&!k?null:(0,d.jsx)("button",{type:"button",className:(0,h.QP)((0,i.r)({size:b,color:c}),g.className),"data-search":"","aria-label":"Open Search",onClick:()=>{j(!0)},children:(0,d.jsx)(e.vj,{})})}function k({hideIfDisabled:a,...b}){let{enabled:c,hotKey:i,setOpenSearch:j}=(0,f.$A)(),{text:k}=(0,g.useI18n)();return a&&!c?null:(0,d.jsxs)("button",{type:"button","data-search-full":"",...b,className:(0,h.QP)("inline-flex items-center gap-2 rounded-lg border bg-fd-secondary/50 p-1.5 ps-2 text-sm text-fd-muted-foreground transition-colors hover:bg-fd-accent hover:text-fd-accent-foreground",b.className),onClick:()=>{j(!0)},children:[(0,d.jsx)(e.vj,{className:"size-4"}),k.search,(0,d.jsx)("div",{className:"ms-auto inline-flex gap-0.5",children:i.map((a,b)=>(0,d.jsx)("kbd",{className:"rounded-md border bg-fd-background px-1.5",children:a.display},b))})]})}},1934:(a,b,c)=>{"use strict";c.d(b,{QueryProvider:()=>g});var d=c(13486),e=c(84364),f=c(53720);function g({children:a}){return(0,d.jsxs)(e.Ht,{client:f.q,children:[a,!1]})}},2470:(a,b,c)=>{"use strict";c.d(b,{LargeSearchToggle:()=>f,SearchToggle:()=>e});var d=c(66352);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call SearchToggle() from the server but SearchToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/search-toggle.js","SearchToggle"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call LargeSearchToggle() from the server but LargeSearchToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/search-toggle.js","LargeSearchToggle")},2747:(a,b,c)=>{"use strict";c.d(b,{s:()=>d});let d={pageTree:[{type:"page",name:"Documentation",url:"/docs",children:[{type:"page",name:"Getting Started",url:"/docs/getting-started",children:[{type:"page",name:"Quick Start",url:"/docs/getting-started/quick-start",children:[]}]},{type:"page",name:"IDE Guides",url:"/docs/ides",children:[{type:"page",name:"Cursor",url:"/docs/ides/cursor",children:[]}]},{type:"page",name:"API Reference",url:"/docs/api",children:[{type:"page",name:"Overview",url:"/docs/api/overview",children:[]}]},{type:"page",name:"Guides",url:"/docs/guides",children:[{type:"page",name:"SEO Setup",url:"/docs/guides/seo-setup",children:[]},{type:"page",name:"Internationalization",url:"/docs/guides/i18n",children:[]},{type:"page",name:"Radix UI Theme",url:"/docs/guides/radix-ui-theme",children:[]}]}]}],getPage:a=>{let{DocsContent:b}=c(16147),d=a?a.join("/"):"index",e={index:{title:"Welcome to OnlyRules Documentation",description:"Learn how to use OnlyRules AI Prompt Management Platform",body:b.index},"getting-started/quick-start":{title:"Quick Start Guide",description:"Get started with OnlyRules in just a few minutes",body:b["getting-started/quick-start"]},"ides/cursor":{title:"Cursor IDE Integration",description:"Learn how to use OnlyRules with Cursor IDE",body:b["ides/cursor"]},"api/overview":{title:"API Overview",description:"Learn about the OnlyRules API endpoints and how to use them",body:b["api/overview"]},"guides/seo-setup":{title:"SEO Setup for OnlyRules",description:"Learn how to configure SEO enhancements for the OnlyRules Next.js application",body:b["guides/seo-setup"]},"guides/i18n":{title:"Internationalization (i18n) Guide",description:"Learn how to use Lingui.js for internationalization in OnlyRules",body:b["guides/i18n"]},"guides/radix-ui-theme":{title:"Radix UI Theme v3 Guide",description:"Learn how to use Radix UI Theme v3 in the OnlyRules project",body:b["guides/radix-ui-theme"]}}[d];return e?{data:{title:e.title,description:e.description,body:e.body,toc:[],full:!1,structuredData:{title:e.title,description:e.description}}}:null},generateParams:()=>[{slug:["index"]},{slug:["getting-started","quick-start"]},{slug:["ides","cursor"]},{slug:["api","overview"]},{slug:["guides","seo-setup"]},{slug:["guides","i18n"]},{slug:["guides","radix-ui-theme"]}],getPages:()=>[{slug:["index"]},{slug:["getting-started","quick-start"]},{slug:["ides","cursor"]},{slug:["api","overview"]},{slug:["guides","seo-setup"]},{slug:["guides","i18n"]},{slug:["guides","radix-ui-theme"]}]}},2984:(a,b,c)=>{"use strict";var d=c(860);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}})},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3674:(a,b,c)=>{"use strict";c.d(b,{HideIfEmpty:()=>e});var d=c(66352);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call HideIfEmpty() from the server but HideIfEmpty is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/hide-if-empty.js","HideIfEmpty");(0,d.registerClientReference)(function(){throw Error("Attempted to call HideIfEmptyProvider() from the server but HideIfEmptyProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/hide-if-empty.js","HideIfEmptyProvider")},3980:(a,b,c)=>{"use strict";c.d(b,{ToasterProvider:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call ToasterProvider() from the server but ToasterProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/toaster-provider.tsx","ToasterProvider")},4627:(a,b,c)=>{"use strict";function d(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}c.d(b,{$:()=>d})},6482:(a,b,c)=>{"use strict";c.d(b,{RootToggle:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call RootToggle() from the server but RootToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/root-toggle.js","RootToggle")},7707:(a,b,c)=>{Promise.resolve().then(c.bind(c,71896)),Promise.resolve().then(c.bind(c,57999)),Promise.resolve().then(c.bind(c,77795)),Promise.resolve().then(c.bind(c,57544)),Promise.resolve().then(c.bind(c,95284))},8512:(a,b,c)=>{"use strict";c.d(b,{QP:()=>aa});let d=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],e=b.nextPart.get(c),f=e?d(a.slice(1),e):void 0;if(f)return f;if(0===b.validators.length)return;let g=a.join("-");return b.validators.find(({validator:a})=>a(g))?.classGroupId},e=/^\[(.+)\]$/,f=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:g(b,a)).classGroupId=c;return}if("function"==typeof a)return h(a)?void f(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{f(e,g(b,a),c,d)})})},g=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},h=a=>a.isThemeGetter,i=/\s+/;function j(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=k(a))&&(d&&(d+=" "),d+=b);return d}let k=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=k(a[d]))&&(c&&(c+=" "),c+=b);return c},l=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},m=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,n=/^\((?:(\w[\w-]*):)?(.+)\)$/i,o=/^\d+\/\d+$/,p=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,q=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,r=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,s=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,t=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,u=a=>o.test(a),v=a=>!!a&&!Number.isNaN(Number(a)),w=a=>!!a&&Number.isInteger(Number(a)),x=a=>a.endsWith("%")&&v(a.slice(0,-1)),y=a=>p.test(a),z=()=>!0,A=a=>q.test(a)&&!r.test(a),B=()=>!1,C=a=>s.test(a),D=a=>t.test(a),E=a=>!G(a)&&!M(a),F=a=>T(a,X,B),G=a=>m.test(a),H=a=>T(a,Y,A),I=a=>T(a,Z,v),J=a=>T(a,V,B),K=a=>T(a,W,D),L=a=>T(a,_,C),M=a=>n.test(a),N=a=>U(a,Y),O=a=>U(a,$),P=a=>U(a,V),Q=a=>U(a,X),R=a=>U(a,W),S=a=>U(a,_,!0),T=(a,b,c)=>{let d=m.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},U=(a,b,c=!1)=>{let d=n.exec(a);return!!d&&(d[1]?b(d[1]):c)},V=a=>"position"===a||"percentage"===a,W=a=>"image"===a||"url"===a,X=a=>"length"===a||"size"===a||"bg-size"===a,Y=a=>"length"===a,Z=a=>"number"===a,$=a=>"family-name"===a,_=a=>"shadow"===a;Symbol.toStringTag;let aa=function(a,...b){let c,g,h,k=function(i){let j;return g=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((j=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(j),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(j),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)f(c[a],d,a,b);return d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:g}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),d(c,b)||(a=>{if(e.test(a)){let b=e.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let d=c[a]||[];return b&&g[a]?[...d,...g[a]]:d}}})(j)}).cache.get,h=c.cache.set,k=l,l(i)};function l(a){let b=g(a);if(b)return b;let d=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(i),j="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:i,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(i){j=b+(j.length>0?" "+j:j);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){j=b+(j.length>0?" "+j:j);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}j=b+(j.length>0?" "+j:j)}return j})(a,c);return h(a,d),d}return function(){return k(j.apply(null,arguments))}}(()=>{let a=l("color"),b=l("font"),c=l("text"),d=l("font-weight"),e=l("tracking"),f=l("leading"),g=l("breakpoint"),h=l("container"),i=l("spacing"),j=l("radius"),k=l("shadow"),m=l("inset-shadow"),n=l("text-shadow"),o=l("drop-shadow"),p=l("blur"),q=l("perspective"),r=l("aspect"),s=l("ease"),t=l("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...B(),M,G],D=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto","contain","none"],U=()=>[M,G,i],V=()=>[u,"full","auto",...U()],W=()=>[w,"none","subgrid",M,G],X=()=>["auto",{span:["full",w,M,G]},w,M,G],Y=()=>[w,"auto",M,G],Z=()=>["auto","min","max","fr",M,G],$=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],aa=()=>["auto",...U()],ab=()=>[u,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...U()],ac=()=>[a,M,G],ad=()=>[...B(),P,J,{position:[M,G]}],ae=()=>["no-repeat",{repeat:["","x","y","space","round"]}],af=()=>["auto","cover","contain",Q,F,{size:[M,G]}],ag=()=>[x,N,H],ah=()=>["","none","full",j,M,G],ai=()=>["",v,N,H],aj=()=>["solid","dashed","dotted","double"],ak=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],al=()=>[v,x,P,J],am=()=>["","none",p,M,G],an=()=>["none",v,M,G],ao=()=>["none",v,M,G],ap=()=>[v,M,G],aq=()=>[u,"full",...U()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[y],breakpoint:[y],color:[z],container:[y],"drop-shadow":[y],ease:["in","out","in-out"],font:[E],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[y],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[y],shadow:[y],spacing:["px",v],text:[y],"text-shadow":[y],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",u,G,M,r]}],container:["container"],columns:[{columns:[v,G,M,h]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:D()}],"overflow-x":[{"overflow-x":D()}],"overflow-y":[{"overflow-y":D()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:V()}],"inset-x":[{"inset-x":V()}],"inset-y":[{"inset-y":V()}],start:[{start:V()}],end:[{end:V()}],top:[{top:V()}],right:[{right:V()}],bottom:[{bottom:V()}],left:[{left:V()}],visibility:["visible","invisible","collapse"],z:[{z:[w,"auto",M,G]}],basis:[{basis:[u,"full","auto",h,...U()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[v,u,"auto","initial","none",G]}],grow:[{grow:["",v,M,G]}],shrink:[{shrink:["",v,M,G]}],order:[{order:[w,"first","last","none",M,G]}],"grid-cols":[{"grid-cols":W()}],"col-start-end":[{col:X()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":W()}],"row-start-end":[{row:X()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:U()}],"gap-x":[{"gap-x":U()}],"gap-y":[{"gap-y":U()}],"justify-content":[{justify:[...$(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...$()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":$()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:U()}],px:[{px:U()}],py:[{py:U()}],ps:[{ps:U()}],pe:[{pe:U()}],pt:[{pt:U()}],pr:[{pr:U()}],pb:[{pb:U()}],pl:[{pl:U()}],m:[{m:aa()}],mx:[{mx:aa()}],my:[{my:aa()}],ms:[{ms:aa()}],me:[{me:aa()}],mt:[{mt:aa()}],mr:[{mr:aa()}],mb:[{mb:aa()}],ml:[{ml:aa()}],"space-x":[{"space-x":U()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":U()}],"space-y-reverse":["space-y-reverse"],size:[{size:ab()}],w:[{w:[h,"screen",...ab()]}],"min-w":[{"min-w":[h,"screen","none",...ab()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...ab()]}],h:[{h:["screen","lh",...ab()]}],"min-h":[{"min-h":["screen","lh","none",...ab()]}],"max-h":[{"max-h":["screen","lh",...ab()]}],"font-size":[{text:["base",c,N,H]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,M,I]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",x,G]}],"font-family":[{font:[O,G,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,M,G]}],"line-clamp":[{"line-clamp":[v,"none",M,I]}],leading:[{leading:[f,...U()]}],"list-image":[{"list-image":["none",M,G]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",M,G]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ac()}],"text-color":[{text:ac()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...aj(),"wavy"]}],"text-decoration-thickness":[{decoration:[v,"from-font","auto",M,H]}],"text-decoration-color":[{decoration:ac()}],"underline-offset":[{"underline-offset":[v,"auto",M,G]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:U()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",M,G]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",M,G]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ad()}],"bg-repeat":[{bg:ae()}],"bg-size":[{bg:af()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},w,M,G],radial:["",M,G],conic:[w,M,G]},R,K]}],"bg-color":[{bg:ac()}],"gradient-from-pos":[{from:ag()}],"gradient-via-pos":[{via:ag()}],"gradient-to-pos":[{to:ag()}],"gradient-from":[{from:ac()}],"gradient-via":[{via:ac()}],"gradient-to":[{to:ac()}],rounded:[{rounded:ah()}],"rounded-s":[{"rounded-s":ah()}],"rounded-e":[{"rounded-e":ah()}],"rounded-t":[{"rounded-t":ah()}],"rounded-r":[{"rounded-r":ah()}],"rounded-b":[{"rounded-b":ah()}],"rounded-l":[{"rounded-l":ah()}],"rounded-ss":[{"rounded-ss":ah()}],"rounded-se":[{"rounded-se":ah()}],"rounded-ee":[{"rounded-ee":ah()}],"rounded-es":[{"rounded-es":ah()}],"rounded-tl":[{"rounded-tl":ah()}],"rounded-tr":[{"rounded-tr":ah()}],"rounded-br":[{"rounded-br":ah()}],"rounded-bl":[{"rounded-bl":ah()}],"border-w":[{border:ai()}],"border-w-x":[{"border-x":ai()}],"border-w-y":[{"border-y":ai()}],"border-w-s":[{"border-s":ai()}],"border-w-e":[{"border-e":ai()}],"border-w-t":[{"border-t":ai()}],"border-w-r":[{"border-r":ai()}],"border-w-b":[{"border-b":ai()}],"border-w-l":[{"border-l":ai()}],"divide-x":[{"divide-x":ai()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ai()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...aj(),"hidden","none"]}],"divide-style":[{divide:[...aj(),"hidden","none"]}],"border-color":[{border:ac()}],"border-color-x":[{"border-x":ac()}],"border-color-y":[{"border-y":ac()}],"border-color-s":[{"border-s":ac()}],"border-color-e":[{"border-e":ac()}],"border-color-t":[{"border-t":ac()}],"border-color-r":[{"border-r":ac()}],"border-color-b":[{"border-b":ac()}],"border-color-l":[{"border-l":ac()}],"divide-color":[{divide:ac()}],"outline-style":[{outline:[...aj(),"none","hidden"]}],"outline-offset":[{"outline-offset":[v,M,G]}],"outline-w":[{outline:["",v,N,H]}],"outline-color":[{outline:ac()}],shadow:[{shadow:["","none",k,S,L]}],"shadow-color":[{shadow:ac()}],"inset-shadow":[{"inset-shadow":["none",m,S,L]}],"inset-shadow-color":[{"inset-shadow":ac()}],"ring-w":[{ring:ai()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ac()}],"ring-offset-w":[{"ring-offset":[v,H]}],"ring-offset-color":[{"ring-offset":ac()}],"inset-ring-w":[{"inset-ring":ai()}],"inset-ring-color":[{"inset-ring":ac()}],"text-shadow":[{"text-shadow":["none",n,S,L]}],"text-shadow-color":[{"text-shadow":ac()}],opacity:[{opacity:[v,M,G]}],"mix-blend":[{"mix-blend":[...ak(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ak()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[v]}],"mask-image-linear-from-pos":[{"mask-linear-from":al()}],"mask-image-linear-to-pos":[{"mask-linear-to":al()}],"mask-image-linear-from-color":[{"mask-linear-from":ac()}],"mask-image-linear-to-color":[{"mask-linear-to":ac()}],"mask-image-t-from-pos":[{"mask-t-from":al()}],"mask-image-t-to-pos":[{"mask-t-to":al()}],"mask-image-t-from-color":[{"mask-t-from":ac()}],"mask-image-t-to-color":[{"mask-t-to":ac()}],"mask-image-r-from-pos":[{"mask-r-from":al()}],"mask-image-r-to-pos":[{"mask-r-to":al()}],"mask-image-r-from-color":[{"mask-r-from":ac()}],"mask-image-r-to-color":[{"mask-r-to":ac()}],"mask-image-b-from-pos":[{"mask-b-from":al()}],"mask-image-b-to-pos":[{"mask-b-to":al()}],"mask-image-b-from-color":[{"mask-b-from":ac()}],"mask-image-b-to-color":[{"mask-b-to":ac()}],"mask-image-l-from-pos":[{"mask-l-from":al()}],"mask-image-l-to-pos":[{"mask-l-to":al()}],"mask-image-l-from-color":[{"mask-l-from":ac()}],"mask-image-l-to-color":[{"mask-l-to":ac()}],"mask-image-x-from-pos":[{"mask-x-from":al()}],"mask-image-x-to-pos":[{"mask-x-to":al()}],"mask-image-x-from-color":[{"mask-x-from":ac()}],"mask-image-x-to-color":[{"mask-x-to":ac()}],"mask-image-y-from-pos":[{"mask-y-from":al()}],"mask-image-y-to-pos":[{"mask-y-to":al()}],"mask-image-y-from-color":[{"mask-y-from":ac()}],"mask-image-y-to-color":[{"mask-y-to":ac()}],"mask-image-radial":[{"mask-radial":[M,G]}],"mask-image-radial-from-pos":[{"mask-radial-from":al()}],"mask-image-radial-to-pos":[{"mask-radial-to":al()}],"mask-image-radial-from-color":[{"mask-radial-from":ac()}],"mask-image-radial-to-color":[{"mask-radial-to":ac()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":B()}],"mask-image-conic-pos":[{"mask-conic":[v]}],"mask-image-conic-from-pos":[{"mask-conic-from":al()}],"mask-image-conic-to-pos":[{"mask-conic-to":al()}],"mask-image-conic-from-color":[{"mask-conic-from":ac()}],"mask-image-conic-to-color":[{"mask-conic-to":ac()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ad()}],"mask-repeat":[{mask:ae()}],"mask-size":[{mask:af()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",M,G]}],filter:[{filter:["","none",M,G]}],blur:[{blur:am()}],brightness:[{brightness:[v,M,G]}],contrast:[{contrast:[v,M,G]}],"drop-shadow":[{"drop-shadow":["","none",o,S,L]}],"drop-shadow-color":[{"drop-shadow":ac()}],grayscale:[{grayscale:["",v,M,G]}],"hue-rotate":[{"hue-rotate":[v,M,G]}],invert:[{invert:["",v,M,G]}],saturate:[{saturate:[v,M,G]}],sepia:[{sepia:["",v,M,G]}],"backdrop-filter":[{"backdrop-filter":["","none",M,G]}],"backdrop-blur":[{"backdrop-blur":am()}],"backdrop-brightness":[{"backdrop-brightness":[v,M,G]}],"backdrop-contrast":[{"backdrop-contrast":[v,M,G]}],"backdrop-grayscale":[{"backdrop-grayscale":["",v,M,G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[v,M,G]}],"backdrop-invert":[{"backdrop-invert":["",v,M,G]}],"backdrop-opacity":[{"backdrop-opacity":[v,M,G]}],"backdrop-saturate":[{"backdrop-saturate":[v,M,G]}],"backdrop-sepia":[{"backdrop-sepia":["",v,M,G]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":U()}],"border-spacing-x":[{"border-spacing-x":U()}],"border-spacing-y":[{"border-spacing-y":U()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",M,G]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[v,"initial",M,G]}],ease:[{ease:["linear","initial",s,M,G]}],delay:[{delay:[v,M,G]}],animate:[{animate:["none",t,M,G]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[q,M,G]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:an()}],"rotate-x":[{"rotate-x":an()}],"rotate-y":[{"rotate-y":an()}],"rotate-z":[{"rotate-z":an()}],scale:[{scale:ao()}],"scale-x":[{"scale-x":ao()}],"scale-y":[{"scale-y":ao()}],"scale-z":[{"scale-z":ao()}],"scale-3d":["scale-3d"],skew:[{skew:ap()}],"skew-x":[{"skew-x":ap()}],"skew-y":[{"skew-y":ap()}],transform:[{transform:[M,G,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:aq()}],"translate-x":[{"translate-x":aq()}],"translate-y":[{"translate-y":aq()}],"translate-z":[{"translate-z":aq()}],"translate-none":["translate-none"],accent:[{accent:ac()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ac()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",M,G]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":U()}],"scroll-mx":[{"scroll-mx":U()}],"scroll-my":[{"scroll-my":U()}],"scroll-ms":[{"scroll-ms":U()}],"scroll-me":[{"scroll-me":U()}],"scroll-mt":[{"scroll-mt":U()}],"scroll-mr":[{"scroll-mr":U()}],"scroll-mb":[{"scroll-mb":U()}],"scroll-ml":[{"scroll-ml":U()}],"scroll-p":[{"scroll-p":U()}],"scroll-px":[{"scroll-px":U()}],"scroll-py":[{"scroll-py":U()}],"scroll-ps":[{"scroll-ps":U()}],"scroll-pe":[{"scroll-pe":U()}],"scroll-pt":[{"scroll-pt":U()}],"scroll-pr":[{"scroll-pr":U()}],"scroll-pb":[{"scroll-pb":U()}],"scroll-pl":[{"scroll-pl":U()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",M,G]}],fill:[{fill:["none",...ac()]}],"stroke-w":[{stroke:[v,N,H,I]}],stroke:[{stroke:["none",...ac()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9650:(a,b,c)=>{"use strict";c.r(b),c.d(b,{I18nContext:()=>f,I18nLabel:()=>g,defaultTranslations:()=>e,useI18n:()=>h});var d=c(60159);let e={search:"Search",searchNoResult:"No results found",toc:"On this page",tocNoHeadings:"No Headings",lastUpdate:"Last updated on",chooseLanguage:"Choose a language",nextPage:"Next Page",previousPage:"Previous Page",chooseTheme:"Theme",editOnGithub:"Edit on GitHub"},f=(0,d.createContext)({text:e});function g(a){let{text:b}=h();return b[a.label]}function h(){return(0,d.useContext)(f)}},10097:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=c(46049),e=c(72610),f=c(90507),g=c(5631),h=c(94924),i=c(8194);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},10393:(a,b,c)=>{"use strict";c.d(b,{JotaiProvider:()=>f});var d=c(13486),e=c(36114);function f({children:a}){return(0,d.jsx)(e.Kq,{children:a})}},10545:(a,b,c)=>{"use strict";a.exports=c(69358).vendored.contexts.RouterContext},10640:(a,b,c)=>{"use strict";c.d(b,{NavProvider:()=>h,hI:()=>i});var d=c(13486),e=c(60159),f=c(86663);(0,f.q6)("StylesContext",{tocNav:"xl:hidden",toc:"max-xl:hidden"});let g=(0,f.q6)("NavContext",{isTransparent:!1});function h({transparentMode:a="none",children:b}){let[c,f]=(0,e.useState)("none"!==a);return(0,d.jsx)(g.Provider,{value:(0,e.useMemo)(()=>({isTransparent:c}),[c]),children:b})}function i(){return g.use()}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13918:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return f}});let d=c(60159),e=()=>{};function f(a){var b;let{headManager:c,reduceComponentsToState:f}=a;function g(){if(c&&c.mountedInstances){let b=d.Children.toArray(Array.from(c.mountedInstances).filter(Boolean));c.updateHead(f(b,a))}}return null==c||null==(b=c.mountedInstances)||b.add(a.children),g(),e(()=>{var b;return null==c||null==(b=c.mountedInstances)||b.add(a.children),()=>{var b;null==c||null==(b=c.mountedInstances)||b.delete(a.children)}}),e(()=>(c&&(c._pendingUpdate=g),()=>{c&&(c._pendingUpdate=g)})),null}},14294:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>H,dynamic:()=>C,metadata:()=>F,revalidate:()=>D,runtime:()=>E,viewport:()=>G});var d=c(38828),e=c(2747),f=c(61365),g=c(73187),h=c(8512),i=c(16499),j=c(80085),k=c(1048),l=c(6482),m=c(69412),n=c(98404),o=c(46964),p=c(69769);let q=(a,b)=>b.icon?{...a,icon:(0,d.jsx)("div",{className:"size-full [&_svg]:size-full max-md:p-1.5 max-md:rounded-md max-md:border max-md:bg-fd-secondary",children:b.icon})}:a;function r(a,{transform:b=q}={}){return function a(c){let d=[];if(c.root){let a=function a(b,c=new Set){for(let d of(b.index&&c.add(b.index.url),b.children))"page"!==d.type||d.external||c.add(d.url),"folder"===d.type&&a(d,c);return c}(c);if(a.size>0){let e={url:a.values().next().value??"",title:c.name,icon:c.icon,description:c.description,urls:a},f=b?b(e,c):e;f&&d.push(f)}}for(let b of c.children)"folder"===b.type&&d.push(...a(b));return d}(a)}function s({item:a,...b}){return"menu"===a.type?(0,d.jsxs)(j.SidebarFolder,{...b,children:[a.url?(0,d.jsxs)(j.SidebarFolderLink,{href:a.url,children:[a.icon,a.text]}):(0,d.jsxs)(j.SidebarFolderTrigger,{children:[a.icon,a.text]}),(0,d.jsx)(j.SidebarFolderContent,{children:a.items.map((a,b)=>(0,d.jsx)(s,{item:a},b))})]}):"custom"===a.type?(0,d.jsx)("div",{...b,children:a.children}):(0,d.jsx)(j.SidebarItem,{href:a.url,icon:a.icon,external:a.external,...b,children:a.text})}var t=c(29038),u=c(71896),v=c(2470),w=c(3674);function x({nav:{transparentMode:a,...b}={},sidebar:{tabs:c,enabled:e=!0,...q}={},searchToggle:x={},disableThemeSwitch:y=!1,themeSwitch:z={enabled:!y},i18n:A=!1,children:B,...C}){let D=(0,f.useMemo)(()=>{var a,b;return a=c,b=C.tree,(Array.isArray(a)?a:"object"==typeof a?r(b,a):!1!==a?r(b):void 0)??[]},[c,C.tree]),E=function(a=[],b){let c=a??[];return b&&(c=[...c,{type:"icon",url:b,text:"Github",label:"GitHub",icon:(0,d.jsx)("svg",{role:"img",viewBox:"0 0 24 24",fill:"currentColor",children:(0,d.jsx)("path",{d:"M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"})}),external:!0}]),c}(C.links??[],C.githubUrl),F=(0,h.QP)(e&&"md:[--fd-sidebar-width:268px] lg:[--fd-sidebar-width:286px]","xl:[--fd-toc-width:286px]",b.component||!1===b.enabled?void 0:"[--fd-nav-height:56px] md:[--fd-nav-height:0px]");return(0,d.jsx)(o.TreeContextProvider,{tree:C.tree,children:(0,d.jsxs)(t.NavProvider,{transparentMode:a,children:[!1!==b.enabled&&(b.component??(0,d.jsxs)(n.Navbar,{className:"h-14 md:hidden",children:[(0,d.jsx)(u.default,{href:b.url??"/",className:"inline-flex items-center gap-2.5 font-semibold",children:b.title}),(0,d.jsx)("div",{className:"flex-1",children:b.children}),!1!==x.enabled&&(x.components?.sm??(0,d.jsx)(v.SearchToggle,{className:"p-2",hideIfDisabled:!0})),e&&(0,d.jsx)(n.SidebarTrigger,{className:(0,h.QP)((0,i.r)({color:"ghost",size:"icon-sm",className:"p-2"})),children:(0,d.jsx)(g.Bx,{})})]})),(0,d.jsxs)(n.LayoutBody,{...C.containerProps,className:(0,h.QP)(F,C.containerProps?.className),children:[e&&function(){let{footer:a,banner:c,collapsible:e=!0,component:f,components:o,defaultOpenLevel:r,prefetch:t,...y}=q;if(f)return f;let B=E.filter(a=>"icon"===a.type),C=(0,d.jsxs)(j.SidebarViewport,{children:[E.filter(a=>"icon"!==a.type).map((a,b,c)=>(0,d.jsx)(s,{item:a,className:(0,h.QP)(b===c.length-1&&"mb-4")},b)),(0,d.jsx)(j.SidebarPageTree,{components:o})]}),F=(0,d.jsxs)(j.SidebarContentMobile,{...y,children:[(0,d.jsxs)(w.HideIfEmpty,{as:j.SidebarHeader,children:[(0,d.jsxs)("div",{className:"flex text-fd-muted-foreground items-center justify-end empty:hidden",children:[B.map((a,b)=>(0,d.jsx)(k.BaseLinkItem,{item:a,className:(0,h.QP)((0,i.r)({size:"icon-sm",color:"ghost",className:"p-2"}),b===B.length-1&&"me-auto"),"aria-label":a.label,children:a.icon},b)),A?(0,d.jsxs)(m.LanguageToggle,{children:[(0,d.jsx)(g.AT,{className:"size-4.5"}),(0,d.jsx)(m.LanguageToggleText,{})]}):null,!1!==z.enabled&&(z.component??(0,d.jsx)(p.ThemeToggle,{className:"p-0 ms-1.5",mode:z.mode})),(0,d.jsx)(n.SidebarTrigger,{className:(0,h.QP)((0,i.r)({color:"ghost",size:"icon-sm",className:"p-2 ms-1.5"})),children:(0,d.jsx)(g.Bx,{})})]}),D.length>0&&(0,d.jsx)(l.RootToggle,{options:D}),c]}),C,(0,d.jsx)(j.SidebarFooter,{className:"empty:hidden",children:a})]}),G=(0,d.jsxs)(j.SidebarContent,{...y,children:[(0,d.jsxs)(j.SidebarHeader,{children:[(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)(u.default,{href:b.url??"/",className:"inline-flex text-[15px] items-center gap-2.5 font-medium me-auto",children:b.title}),b.children,e&&(0,d.jsx)(j.SidebarCollapseTrigger,{className:(0,h.QP)((0,i.r)({color:"ghost",size:"icon-sm",className:"mb-auto text-fd-muted-foreground"})),children:(0,d.jsx)(g.Bx,{})})]}),!1!==x.enabled&&(x.components?.lg??(0,d.jsx)(v.LargeSearchToggle,{hideIfDisabled:!0})),D.length>0&&(0,d.jsx)(l.RootToggle,{options:D}),c]}),C,(0,d.jsxs)(w.HideIfEmpty,{as:j.SidebarFooter,children:[(0,d.jsxs)("div",{className:"flex text-fd-muted-foreground items-center justify-end empty:hidden",children:[B.map((a,b)=>(0,d.jsx)(k.BaseLinkItem,{item:a,className:(0,h.QP)((0,i.r)({size:"icon-sm",color:"ghost"}),b===B.length-1&&"me-auto"),"aria-label":a.label,children:a.icon},b)),A?(0,d.jsx)(m.LanguageToggle,{children:(0,d.jsx)(g.AT,{className:"size-4.5"})}):null,!1!==z.enabled&&(z.component??(0,d.jsx)(p.ThemeToggle,{className:"p-0 ms-1.5",mode:z.mode}))]}),a]})]});return(0,d.jsx)(j.Sidebar,{defaultOpenLevel:r,prefetch:t,Mobile:F,Content:(0,d.jsxs)(d.Fragment,{children:[e&&(0,d.jsx)(n.CollapsibleControl,{}),G]})})}(),B]})]})})}var y=c(86987),z=c(99023),A=c(79104),B=c(3980);c(37087);let C="force-dynamic",D=0,E="nodejs",F={metadataBase:new URL("https://onlyrules.codes"),title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.",keywords:"AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, Junie, Trae, Lingma, Kiro, Tencent Cloud CodeBuddy",authors:[{name:"OnlyRules Team"}],publisher:"OnlyRules",category:"Technology",classification:"Software Development Tools",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs.",type:"website",locale:"en_US",siteName:"OnlyRules"},twitter:{card:"summary_large_image",title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs."},alternates:{canonical:"https://onlyrules.codes"}},G={width:"device-width",initialScale:1,maximumScale:5,userScalable:!0,themeColor:[{media:"(prefers-color-scheme: light)",color:"hsl(0 0% 100%)"},{media:"(prefers-color-scheme: dark)",color:"hsl(240 10% 3.9%)"}]};function H({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{suppressHydrationWarning:!0,children:(0,d.jsx)(z.JotaiProvider,{children:(0,d.jsxs)(A.QueryProvider,{children:[(0,d.jsx)(y.RootProvider,{children:(0,d.jsx)(x,{tree:{name:"Docs",children:e.s.pageTree},nav:{title:"OnlyRules Docs",url:"/docs"},sidebar:{defaultOpenLevel:1},links:[{text:"Home",url:"/"},{text:"Dashboard",url:"/dashboard"},{text:"GitHub",url:"https://github.com/ranglang/onlyrules",external:!0}],children:a})}),(0,d.jsx)(B.ToasterProvider,{})]})})})})}},15122:(a,b,c)=>{"use strict";c.d(b,{ToasterProvider:()=>i});var d=c(13486),e=c(60159),f=c(48961),g=c(81604);let h=({...a})=>{let{theme:b="system"}=(0,f.D)(),[c,h]=(0,e.useState)(!1);return((0,e.useEffect)(()=>{h(!0)},[]),c)?(0,d.jsx)(g.l$,{theme:b,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...a}):null};function i(){let[a,b]=(0,e.useState)(!1);return a?(0,d.jsx)(h,{}):null}},16147:(a,b,c)=>{"use strict";c.r(b),c.d(b,{DocsContent:()=>e});var d=c(38828);c(61365);let e={index:()=>(0,d.jsxs)("div",{className:"prose prose-slate max-w-none dark:prose-invert",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold text-foreground mb-6",children:"Welcome to OnlyRules Documentation"}),(0,d.jsx)("p",{className:"text-lg text-muted-foreground leading-relaxed mb-8",children:"OnlyRules is an AI Prompt Management Platform that helps developers create, organize, and share AI prompt rules for their favorite IDEs."}),(0,d.jsx)("h2",{className:"text-2xl font-semibold text-foreground mt-8 mb-4",children:"Quick Start"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"Get started with OnlyRules in just a few minutes:"}),(0,d.jsxs)("ol",{className:"list-decimal list-inside space-y-2 ml-4 text-muted-foreground",children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{className:"text-foreground",children:"Sign up"})," for an account at OnlyRules"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{className:"text-foreground",children:"Browse"})," existing templates and rules"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{className:"text-foreground",children:"Create"})," your own custom rules"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{className:"text-foreground",children:"Share"})," with the community"]})]}),(0,d.jsx)("h2",{className:"text-2xl font-semibold text-foreground mt-8 mb-4",children:"Features"}),(0,d.jsxs)("ul",{className:"list-disc list-inside space-y-2 ml-4",children:[(0,d.jsxs)("li",{className:"text-muted-foreground",children:["\uD83D\uDE80 ",(0,d.jsx)("strong",{className:"text-foreground",children:"IDE Integration"})," - Works with Cursor, Windsurf, GitHub Copilot, Claude, and more"]}),(0,d.jsxs)("li",{className:"text-muted-foreground",children:["\uD83D\uDCDD ",(0,d.jsx)("strong",{className:"text-foreground",children:"Rule Management"})," - Create, edit, and organize your AI prompt rules"]}),(0,d.jsxs)("li",{className:"text-muted-foreground",children:["\uD83C\uDF10 ",(0,d.jsx)("strong",{className:"text-foreground",children:"Community Sharing"})," - Share and discover rules from other developers"]}),(0,d.jsxs)("li",{className:"text-muted-foreground",children:["\uD83C\uDFA8 ",(0,d.jsx)("strong",{className:"text-foreground",children:"Template System"})," - Use pre-built templates to get started quickly"]}),(0,d.jsxs)("li",{className:"text-muted-foreground",children:["\uD83D\uDD0D ",(0,d.jsx)("strong",{className:"text-foreground",children:"Search & Discovery"})," - Find the perfect rules for your use case"]})]}),(0,d.jsx)("h2",{className:"text-2xl font-semibold text-foreground mt-8 mb-4",children:"Popular IDEs Supported"}),(0,d.jsxs)("ul",{className:"list-disc list-inside space-y-2 ml-4",children:[(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/docs/ides/cursor",className:"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline",children:"Cursor"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/docs/ides/windsurf",className:"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline",children:"Windsurf"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/docs/ides/github-copilot",className:"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline",children:"GitHub Copilot"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/docs/ides/claude",className:"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline",children:"Claude"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/docs/ides/cline",className:"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline",children:"Cline"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/docs/ides/augment",className:"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline",children:"Augment Code"})})]})]}),"getting-started/quick-start":()=>(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{children:"Quick Start Guide"}),(0,d.jsx)("p",{children:"Welcome to OnlyRules! This guide will help you get started with creating and using AI prompt rules for your favorite IDEs."}),(0,d.jsx)("h2",{children:"What is OnlyRules?"}),(0,d.jsx)("p",{children:"OnlyRules is an AI Prompt Management Platform that helps developers:"}),(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:"Create custom AI prompt rules for different IDEs"}),(0,d.jsx)("li",{children:"Share and discover community-created rules"}),(0,d.jsx)("li",{children:"Organize rules with tags and categories"}),(0,d.jsx)("li",{children:"Export rules in various formats"})]}),(0,d.jsx)("h2",{children:"Step 1: Create an Account"}),(0,d.jsxs)("ol",{children:[(0,d.jsxs)("li",{children:["Visit ",(0,d.jsx)("a",{href:"/",children:"OnlyRules"})]}),(0,d.jsxs)("li",{children:["Click ",(0,d.jsx)("strong",{children:"Sign Up"})," in the navigation"]}),(0,d.jsx)("li",{children:"Fill in your details and create your account"}),(0,d.jsx)("li",{children:"Verify your email address"})]}),(0,d.jsx)("h2",{children:"Step 2: Browse Existing Rules"}),(0,d.jsx)("p",{children:"Before creating your own rules, explore what's already available:"}),(0,d.jsxs)("ol",{children:[(0,d.jsxs)("li",{children:["Go to the ",(0,d.jsx)("a",{href:"/dashboard",children:"Dashboard"})]}),(0,d.jsx)("li",{children:"Browse rules by IDE type or tags"}),(0,d.jsx)("li",{children:"Click on any rule to view its content"}),(0,d.jsx)("li",{children:"Use the search functionality to find specific rules"})]})]}),"ides/cursor":()=>(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{children:"Cursor IDE Integration"}),(0,d.jsx)("p",{children:"Cursor is an AI-powered code editor that makes it easy to integrate custom AI prompt rules. This guide shows you how to use OnlyRules with Cursor."}),(0,d.jsx)("h2",{children:"What is Cursor?"}),(0,d.jsx)("p",{children:"Cursor is a fork of VS Code that's designed from the ground up to be the best way to code with AI. It features:"}),(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:"Built-in AI chat and code generation"}),(0,d.jsx)("li",{children:"Custom rule support"}),(0,d.jsx)("li",{children:"Advanced AI features like Composer and Tab completion"}),(0,d.jsx)("li",{children:"Full VS Code compatibility"})]}),(0,d.jsx)("h2",{children:"Setting Up Rules in Cursor"}),(0,d.jsx)("h3",{children:"Method 1: Using .cursorrules File"}),(0,d.jsxs)("ol",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Create a .cursorrules file"})," in your project root"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Copy rule content"})," from OnlyRules"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Paste into .cursorrules"})," file"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Save the file"})," - Cursor will automatically detect it"]})]})]}),"api/overview":()=>(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{children:"API Overview"}),(0,d.jsx)("p",{children:"The OnlyRules API provides programmatic access to rules, templates, and user data. All API endpoints are RESTful and return JSON responses."}),(0,d.jsx)("h2",{children:"Base URL"}),(0,d.jsx)("pre",{children:(0,d.jsx)("code",{children:"https://onlyrules.app/api"})}),(0,d.jsx)("h2",{children:"Authentication"}),(0,d.jsx)("p",{children:"Most API endpoints require authentication. Include your API key in the Authorization header:"}),(0,d.jsx)("pre",{children:(0,d.jsx)("code",{children:`curl -H "Authorization: Bearer YOUR_API_KEY" \\
  https://onlyrules.app/api/rules`})}),(0,d.jsx)("h2",{children:"Available Endpoints"}),(0,d.jsx)("h3",{children:"Rules API"}),(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("code",{children:"GET /api/rules"})," - List all rules"]}),(0,d.jsxs)("li",{children:[(0,d.jsxs)("code",{children:["GET /api/rules/","{id}"]})," - Get a specific rule"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("code",{children:"POST /api/rules"})," - Create a new rule"]}),(0,d.jsxs)("li",{children:[(0,d.jsxs)("code",{children:["PUT /api/rules/","{id}"]})," - Update a rule"]}),(0,d.jsxs)("li",{children:[(0,d.jsxs)("code",{children:["DELETE /api/rules/","{id}"]})," - Delete a rule"]})]})]}),"guides/seo-setup":()=>(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{children:"SEO Setup for OnlyRules"}),(0,d.jsx)("p",{children:"This document outlines the SEO enhancements implemented for the OnlyRules Next.js application."}),(0,d.jsx)("h2",{children:"Implemented Features"}),(0,d.jsx)("h3",{children:"1. Dynamic Sitemap Generation"}),(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:["Automatically generates a sitemap at ",(0,d.jsx)("code",{children:"/sitemap.xml"})]}),(0,d.jsx)("li",{children:"Uses API routes for Next.js compatibility"}),(0,d.jsx)("li",{children:"Includes all static routes with appropriate priorities"})]}),(0,d.jsx)("h3",{children:"2. Robots.txt Configuration"}),(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:"Dynamically generates robots.txt"}),(0,d.jsx)("li",{children:"Allows crawlers to access public pages"}),(0,d.jsx)("li",{children:"Disallows access to private directories"})]})]}),"guides/i18n":()=>(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{children:"Internationalization (i18n) Guide"}),(0,d.jsx)("p",{children:"This project uses Lingui.js for internationalization, supporting English (en), Simplified Chinese (zh-CN), and Traditional Chinese (zh-HK)."}),(0,d.jsx)("h2",{children:"Quick Start"}),(0,d.jsx)("h3",{children:"Using Translations in Components"}),(0,d.jsxs)("p",{children:["Use the ",(0,d.jsx)("code",{children:"useLingui"})," hook from ",(0,d.jsx)("code",{children:"@lingui/react"}),":"]}),(0,d.jsx)("pre",{children:(0,d.jsx)("code",{children:`'use client'

import { useLingui } from '@lingui/react'

export function MyComponent() {
  const { i18n } = useLingui()
  
  return (
    <div>
      <h1>{i18n._("hero.title")}</h1>
      <p>{i18n._("hero.description")}</p>
    </div>
  )
}`})})]}),"guides/radix-ui-theme":()=>(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{children:"Radix UI Theme v3 Guide"}),(0,d.jsx)("p",{children:"This guide explains how to use Radix UI Theme v3 in the OnlyRules project."}),(0,d.jsx)("h2",{children:"Overview"}),(0,d.jsx)("p",{children:"We've migrated from a mixed Tailwind/custom CSS approach to using Radix UI Theme v3's design system. This provides:"}),(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:"Consistent design tokens"}),(0,d.jsx)("li",{children:"Built-in dark mode support"}),(0,d.jsx)("li",{children:"Accessible components"}),(0,d.jsx)("li",{children:"Responsive design patterns"}),(0,d.jsx)("li",{children:"Better performance"})]}),(0,d.jsx)("h2",{children:"Key Changes"}),(0,d.jsx)("h3",{children:"1. Component Usage"}),(0,d.jsx)("p",{children:"Instead of using HTML elements with Tailwind classes, use Radix UI components:"}),(0,d.jsx)("pre",{children:(0,d.jsx)("code",{children:`// ❌ Old approach
<div className="flex flex-col gap-4">
  <h1 className="text-4xl font-bold">Title</h1>
  <p className="text-gray-600">Description</p>
</div>

// ✅ New approach
<Flex direction="column" gap="4">
  <Heading size="8">Title</Heading>
  <Text color="gray">Description</Text>
</Flex>`})})]})}},16499:(a,b,c)=>{"use strict";c.d(b,{r:()=>f});let d=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,e=function(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d},f=((a,b)=>c=>{var f;if((null==b?void 0:b.variants)==null)return e(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:g,defaultVariants:h}=b,i=Object.keys(g).map(a=>{let b=null==c?void 0:c[a],e=null==h?void 0:h[a];if(null===b)return null;let f=d(b)||d(e);return g[a][f]}),j=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return e(a,i,null==b||null==(f=b.compoundVariants)?void 0:f.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...h,...j}[b]):({...h,...j})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)})("inline-flex items-center justify-center rounded-md p-2 text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none",{variants:{color:{primary:"bg-fd-primary text-fd-primary-foreground hover:bg-fd-primary/80",outline:"border hover:bg-fd-accent hover:text-fd-accent-foreground",ghost:"hover:bg-fd-accent hover:text-fd-accent-foreground",secondary:"border bg-fd-secondary text-fd-secondary-foreground hover:bg-fd-accent hover:text-fd-accent-foreground"},size:{sm:"gap-1 px-2 py-1.5 text-xs",icon:"p-1.5 [&_svg]:size-5","icon-sm":"p-1.5 [&_svg]:size-4.5","icon-xs":"p-1 [&_svg]:size-4"}}})},18323:(a,b,c)=>{Promise.resolve().then(c.bind(c,55703)),Promise.resolve().then(c.bind(c,38689)),Promise.resolve().then(c.bind(c,78041)),Promise.resolve().then(c.bind(c,9650)),Promise.resolve().then(c.bind(c,38098))},19037:(a,b,c)=>{"use strict";c.d(b,{$3:()=>p,AX:()=>n,Bx:()=>j,Gr:()=>m,JG:()=>u,Jl:()=>q,Ml:()=>k,Vw:()=>s,bd:()=>o,c_:()=>t,iU:()=>r,vj:()=>l,yQ:()=>i});var d=c(13486),e=c(60159),f=c(91518);let g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},h=(a,b)=>{let c=(0,e.forwardRef)(({className:a,size:c=24,color:h="currentColor",children:i,...j},k)=>(0,d.jsxs)("svg",{ref:k,...g,width:c,height:c,stroke:h,className:(0,f.QP)("lucide",a),...j,children:[b.map(([a,b])=>(0,e.createElement)(a,b)),i]}));return c.displayName=a,c},i=h("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);h("languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]);let j=h("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]),k=h("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]),l=h("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),m=h("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),n=h("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),o=h("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),p=h("airplay",[["path",{d:"M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1",key:"ns4c3b"}],["path",{d:"m12 15 5 6H7Z",key:"14qnn2"}]]);h("menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),h("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),h("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),h("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),h("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);let q=h("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);h("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),h("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),h("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);let r=h("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),s=h("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);h("text",[["path",{d:"M15 18H3",key:"olowqp"}],["path",{d:"M17 6H3",key:"16j9eg"}],["path",{d:"M21 12H3",key:"2avoz0"}]]),h("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]),h("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]),h("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]),h("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),h("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),h("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);let t=h("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),u=h("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);h("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),h("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),h("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19366:(a,b,c)=>{"use strict";c.d(b,{L:()=>k,TreeContextProvider:()=>j,t:()=>l});var d=c(13486),e=c(86663),f=c(60159),g=c(32043);let h=(0,e.q6)("TreeContext"),i=(0,e.q6)("PathContext",[]);function j(a){let b=(0,f.useRef)(0),c=(0,e.a8)(),j=(0,f.useMemo)(()=>a.tree,[a.tree.$id??a.tree]),k=(0,f.useMemo)(()=>(0,g.oe)(j.children,c)??[],[j,c]),l=k.findLast(a=>"folder"===a.type&&a.root)??j;return l.$id??(l.$id=String(b.current++)),(0,d.jsx)(h.Provider,{value:(0,f.useMemo)(()=>({root:l}),[l]),children:(0,d.jsx)(i.Provider,{value:k,children:a.children})})}function k(){return i.use()}function l(){return h.use("You must wrap this component under <DocsLayout />")}},21944:(a,b,c)=>{"use strict";c.d(b,{G:()=>j,c:()=>i});var d=c(13486),e=c(60159),f=c(86663),g=c(59492);let h=(0,f.q6)("SidebarContext");function i(){return h.use()}function j({children:a}){let b=(0,e.useRef)(!0),[c,i]=(0,e.useState)(!1),[j,k]=(0,e.useState)(!1),l=(0,f.a8)();return(0,g.T)(l,()=>{b.current&&i(!1),b.current=!0}),(0,d.jsx)(h.Provider,{value:(0,e.useMemo)(()=>({open:c,setOpen:i,collapsed:j,setCollapsed:k,closeOnRedirect:b}),[c,j]),children:a})}},21971:()=>{},23850:(a,b,c)=>{"use strict";c.d(b,{LanguageToggle:()=>i,LanguageToggleText:()=>j});var d=c(13486),e=c(9650),f=c(95670),g=c(91518),h=c(93507);function i(a){let b=(0,e.useI18n)();if(!b.locales)throw Error("Missing `<I18nProvider />`");return(0,d.jsxs)(f.AM,{children:[(0,d.jsx)(f.Wv,{"aria-label":b.text.chooseLanguage,...a,className:(0,g.QP)((0,h.r)({color:"ghost",className:"gap-1.5 p-1.5"}),a.className),children:a.children}),(0,d.jsxs)(f.hl,{className:"flex flex-col overflow-hidden p-0",children:[(0,d.jsx)("p",{className:"mb-1 p-2 text-xs font-medium text-fd-muted-foreground",children:b.text.chooseLanguage}),b.locales.map(a=>(0,d.jsx)("button",{type:"button",className:(0,g.QP)("p-2 text-start text-sm",a.locale===b.locale?"bg-fd-primary/10 font-medium text-fd-primary":"hover:bg-fd-accent hover:text-fd-accent-foreground"),onClick:()=>{b.onChange?.(a.locale)},children:a.name},a.locale))]})]})}function j(a){let b=(0,e.useI18n)(),c=b.locales?.find(a=>a.locale===b.locale)?.name;return(0,d.jsx)("span",{...a,children:c})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27923:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return p},defaultHead:function(){return l}});let d=c(50686),e=c(15881),f=c(13486),g=e._(c(60159)),h=d._(c(13918)),i=c(85936),j=c(50872),k=c(38523);function l(a){void 0===a&&(a=!1);let b=[(0,f.jsx)("meta",{charSet:"utf-8"},"charset")];return a||b.push((0,f.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),b}function m(a,b){return"string"==typeof b||"number"==typeof b?a:b.type===g.default.Fragment?a.concat(g.default.Children.toArray(b.props.children).reduce((a,b)=>"string"==typeof b||"number"==typeof b?a:a.concat(b),[])):a.concat(b)}c(12405);let n=["name","httpEquiv","charSet","itemProp"];function o(a,b){let{inAmpMode:c}=b;return a.reduce(m,[]).reverse().concat(l(c).reverse()).filter(function(){let a=new Set,b=new Set,c=new Set,d={};return e=>{let f=!0,g=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){g=!0;let b=e.key.slice(e.key.indexOf("$")+1);a.has(b)?f=!1:a.add(b)}switch(e.type){case"title":case"base":b.has(e.type)?f=!1:b.add(e.type);break;case"meta":for(let a=0,b=n.length;a<b;a++){let b=n[a];if(e.props.hasOwnProperty(b))if("charSet"===b)c.has(b)?f=!1:c.add(b);else{let a=e.props[b],c=d[b]||new Set;("name"!==b||!g)&&c.has(a)?f=!1:(c.add(a),d[b]=c)}}}return f}}()).reverse().map((a,b)=>{let c=a.key||b;return g.default.cloneElement(a,{key:c})})}let p=function(a){let{children:b}=a,c=(0,g.useContext)(i.AmpStateContext),d=(0,g.useContext)(j.HeadManagerContext);return(0,f.jsx)(h.default,{reduceComponentsToState:o,headManager:d,inAmpMode:(0,k.isInAmpMode)(c),children:b})};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},28186:(a,b,c)=>{"use strict";c.d(b,{N_:()=>k,Uy:()=>h,a8:()=>i,q6:()=>l,rd:()=>j});var d=c(60159),e=c(13486),f=()=>{throw Error("You need to wrap your application inside `FrameworkProvider`.")},g=l("FrameworkContext",{useParams:f,useRouter:f,usePathname:f});function h({Link:a,useRouter:b,useParams:c,usePathname:f,Image:h,children:i}){let j=d.useMemo(()=>({usePathname:f,useRouter:b,Link:a,Image:h,useParams:c}),[a,f,b,c,h]);return(0,e.jsx)(g.Provider,{value:j,children:i})}function i(){return g.use().usePathname()}function j(){return g.use().useRouter()}function k(a){let{Link:b}=g.use();if(!b){let{href:b,prefetch:c,...d}=a;return(0,e.jsx)("a",{href:b,...d})}return(0,e.jsx)(b,{...a})}function l(a,b){let c=d.createContext(b);return{Provider:a=>(0,e.jsx)(c.Provider,{value:a.value,children:a.children}),use:b=>{let e=d.useContext(c);if(!e)throw Error(b??`Provider of ${a} is required but missing.`);return e}}}},28354:a=>{"use strict";a.exports=require("util")},29038:(a,b,c)=>{"use strict";c.d(b,{NavProvider:()=>e});var d=c(66352);(0,d.registerClientReference)(function(){throw Error("Attempted to call usePageStyles() from the server but usePageStyles is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/layout.js","usePageStyles"),(0,d.registerClientReference)(function(){throw Error("Attempted to call StylesProvider() from the server but StylesProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/layout.js","StylesProvider");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call NavProvider() from the server but NavProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/layout.js","NavProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useNav() from the server but useNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/layout.js","useNav")},29114:(a,b,c)=>{"use strict";c.d(b,{J:()=>e}),c(68926);var d=c(60159);function e(a){let b=(0,d.useRef)(a);return b.current=a,(0,d.useCallback)((...a)=>b.current(...a),[])}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29699:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"forbidden",{enumerable:!0,get:function(){return d}}),c(22859).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},29958:(a,b,c)=>{"use strict";c.d(b,{RootProvider:()=>v});var d=c(13486),e=c(48961),f=c(60159),g=c(88200),h=c(21944),i=c(30186),j=c(29114),k=c(9650),l=c(86663);let m=(0,f.lazy)(()=>c.e(5247).then(c.bind(c,5247)));function n({children:a,dir:b="ltr",theme:c={},search:f,i18n:j}){let k=a;return f?.enabled!==!1&&(k=(0,d.jsx)(i.YL,{SearchDialog:m,...f,children:k})),c?.enabled!==!1&&(k=(0,d.jsx)(e.N,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,...c,children:k})),j&&(k=(0,d.jsx)(o,{...j,children:k})),(0,d.jsx)(g.FX,{dir:b,children:(0,d.jsx)(h.G,{children:k})})}function o({locales:a=[],locale:b,onLocaleChange:c,...e}){let g=(0,l.rd)(),h=(0,l.a8)(),i=(0,j.J)(a=>{if(c)return c(a);let d=h.split("/").filter(a=>a.length>0);d[0]!==b?d.unshift(a):d[0]=a,g.push(`/${d.join("/")}`),g.refresh()});return(0,d.jsx)(k.I18nContext.Provider,{value:(0,f.useMemo)(()=>({locale:b,locales:a,text:{...k.defaultTranslations,...e.translations},onChange:i}),[b,a,i,e.translations]),children:e.children})}var p=c(28186);c(68926);var q=c(2984),r=c(49989),s=c(44382),t=c.n(s);function u({children:a}){return(0,d.jsx)(p.Uy,{usePathname:q.usePathname,useRouter:q.useRouter,useParams:q.useParams,Link:r,Image:t(),children:a})}function v(a){return(0,d.jsx)(u,{children:(0,d.jsx)(n,{...a,children:a.children})})}c(19366),c(10640)},30186:(a,b,c)=>{"use strict";c.d(b,{$A:()=>g,YL:()=>i});var d=c(13486),e=c(60159);let f=(0,c(86663).q6)("SearchContext",{enabled:!1,hotKey:[],setOpenSearch:()=>void 0});function g(){return f.use()}function h(){let[a,b]=(0,e.useState)("⌘");return a}function i({SearchDialog:a,children:b,preload:c=!0,options:g,hotKey:i=[{key:a=>a.metaKey||a.ctrlKey,display:(0,d.jsx)(h,{})},{key:"k",display:"K"}],links:j}){let[k,l]=(0,e.useState)(!c&&void 0);return(0,d.jsxs)(f.Provider,{value:(0,e.useMemo)(()=>({enabled:!0,hotKey:i,setOpenSearch:l}),[i]),children:[void 0!==k&&(0,d.jsx)(a,{open:k,onOpenChange:l,links:j,...g}),b]})}},30508:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,69355,23)),Promise.resolve().then(c.t.bind(c,54439,23)),Promise.resolve().then(c.t.bind(c,94730,23)),Promise.resolve().then(c.t.bind(c,19774,23)),Promise.resolve().then(c.t.bind(c,53170,23)),Promise.resolve().then(c.t.bind(c,20968,23)),Promise.resolve().then(c.t.bind(c,78298,23)),Promise.resolve().then(c.t.bind(c,10282,23))},32043:(a,b,c)=>{"use strict";function d(a,b,c){let{includePage:d=!0,includeSeparator:e=!1,includeRoot:f}=c,g=[];return b.forEach((a,c)=>{if("separator"===a.type&&a.name&&e&&g.push({name:a.name}),"folder"===a.type){let d=b.at(c+1);if(d&&a.index===d)return;if(a.root){g=[];return}g.push({name:a.name,url:a.index?.url})}"page"===a.type&&d&&g.push({name:a.name,url:a.url})}),f&&g.unshift({name:a.name,url:"object"==typeof f?f.url:void 0}),g}c.d(b,{Pp:()=>d,oe:()=>function a(b,c){let d;for(let e of(c.endsWith("/")&&(c=c.slice(0,-1)),b)){if("separator"===e.type&&(d=e),"folder"===e.type){if(e.index?.url===c){let a=[];return d&&a.push(d),a.push(e,e.index),a}let b=a(e.children,c);if(b)return b.unshift(e),d&&b.unshift(d),b}if("page"===e.type&&e.url===c){let a=[];return d&&a.push(d),a.push(e),a}}return null}}),c(68926),c(60159)},32578:(a,b,c)=>{Promise.resolve().then(c.bind(c,63148))},33873:a=>{"use strict";a.exports=require("path")},35476:(a,b,c)=>{"use strict";c.d(b,{j:()=>k});var d=c(13486),e=c(60159),f=c(48878),g=c(59492),h=c(29114);function i(a,b){if(0===b.length||0===a.clientHeight)return[0,0];let c=Number.MAX_VALUE,d=0;for(let e of b){let b=a.querySelector(`a[href="#${e}"]`);if(!b)continue;let f=getComputedStyle(b);c=Math.min(c,b.offsetTop+parseFloat(f.paddingTop)),d=Math.max(d,b.offsetTop+b.clientHeight-parseFloat(f.paddingBottom))}return[c,d-c]}function j(a,b){a.style.setProperty("--fd-top",`${b[0]}px`),a.style.setProperty("--fd-height",`${b[1]}px`)}function k({containerRef:a,...b}){let c=f.Mf(),k=(0,e.useRef)(null),l=(0,h.J)(()=>{a.current&&k.current&&j(k.current,i(a.current,c))});return(0,e.useEffect)(()=>{if(!a.current)return;let b=a.current;l();let c=new ResizeObserver(l);return c.observe(b),()=>{c.disconnect()}},[a,l]),(0,g.T)(c,()=>{a.current&&k.current&&j(k.current,i(a.current,c))}),(0,d.jsx)("div",{ref:k,role:"none",...b})}},36588:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,30385,23)),Promise.resolve().then(c.t.bind(c,33737,23)),Promise.resolve().then(c.t.bind(c,1904,23)),Promise.resolve().then(c.t.bind(c,35856,23)),Promise.resolve().then(c.t.bind(c,55492,23)),Promise.resolve().then(c.t.bind(c,89082,23)),Promise.resolve().then(c.t.bind(c,45812,23)),Promise.resolve().then(c.bind(c,3220))},37087:()=>{},38050:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"notFound",{enumerable:!0,get:function(){return e}});let d=""+c(22859).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},38098:(a,b,c)=>{"use strict";c.d(b,{PageBreadcrumb:()=>D,PageFooter:()=>B,PageLastUpdate:()=>z,PageRoot:()=>y,PageTOC:()=>E,PageTOCPopover:()=>x,PageTOCPopoverContent:()=>w,PageTOCPopoverTrigger:()=>u});var d=c(13486),e=c(60159),f=c(19037),g=c(55703),h=c(91518),i=c(9650),j=c(19366),k=c(86663),l=c(32043),m=c(10640),n=c(86232),o=c(29114),p=c(93687),q=c(21944),r=c(78041),s=c(48878);let t=(0,k.q6)("TocPopoverContext");function u(a){let{text:b}=(0,i.useI18n)(),{open:c}=t.use(),g=(0,r.a)(),k=(0,s.R3)(),l=(0,e.useMemo)(()=>g.findIndex(a=>k===a.url.slice(1)),[g,k]),m=(0,j.L)().at(-1),n=-1!==l&&!c;return(0,d.jsxs)(p.R6,{...a,className:(0,h.QP)("flex w-full h-(--fd-tocnav-height) items-center text-sm text-fd-muted-foreground gap-2.5 px-4 py-2.5 text-start focus-visible:outline-none [&_svg]:shrink-0 [&_svg]:size-4 md:px-6",a.className),children:[(0,d.jsx)(v,{value:(l+1)/Math.max(1,g.length),max:1,className:(0,h.QP)(c&&"text-fd-primary")}),(0,d.jsxs)("span",{className:"grid flex-1 *:my-auto *:row-start-1 *:col-start-1",children:[(0,d.jsx)("span",{className:(0,h.QP)("truncate transition-all",c&&"text-fd-foreground",n&&"opacity-0 -translate-y-full pointer-events-none"),children:m?.name??b.toc}),(0,d.jsx)("span",{className:(0,h.QP)("truncate transition-all",!n&&"opacity-0 translate-y-full pointer-events-none"),children:g[l]?.title})]}),(0,d.jsx)(f.yQ,{className:(0,h.QP)("transition-transform mx-0.5",c&&"rotate-180")})]})}function v({value:a,strokeWidth:b=2,size:c=24,min:e=0,max:f=100,...g}){let h=a<e?e:a>f?f:a,i=(c-b)/2,j=2*Math.PI*i,k=h/f*j,l={cx:c/2,cy:c/2,r:i,fill:"none",strokeWidth:b};return(0,d.jsxs)("svg",{role:"progressbar",viewBox:`0 0 ${c} ${c}`,"aria-valuenow":h,"aria-valuemin":e,"aria-valuemax":f,...g,children:[(0,d.jsx)("circle",{...l,className:"stroke-current/25"}),(0,d.jsx)("circle",{...l,stroke:"currentColor",strokeDasharray:j,strokeDashoffset:j-k,strokeLinecap:"round",transform:`rotate(-90 ${c/2} ${c/2})`,className:"transition-all"})]})}function w(a){return(0,d.jsx)(p.Ke,{"data-toc-popover":"",...a,className:(0,h.QP)("flex flex-col px-4 max-h-[50vh] md:px-6",a.className),children:a.children})}function x(a){let b=(0,e.useRef)(null),[c,f]=(0,e.useState)(!1),{collapsed:g}=(0,q.c)(),{isTransparent:i}=(0,m.hI)();return(0,o.J)(a=>{c&&b.current&&!b.current.contains(a.target)&&f(!1)}),(0,d.jsx)(t.Provider,{value:(0,e.useMemo)(()=>({open:c,setOpen:f}),[f,c]),children:(0,d.jsx)(p.Nt,{open:c,onOpenChange:f,asChild:!0,children:(0,d.jsx)("header",{ref:b,id:"nd-tocnav",...a,className:(0,h.QP)("fixed inset-x-0 z-10 border-b backdrop-blur-sm transition-colors xl:hidden",(!i||c)&&"bg-fd-background/80",c&&"shadow-lg",a.className),style:{...a.style,top:"calc(var(--fd-banner-height) + var(--fd-nav-height))",insetInlineStart:g?"0px":"calc(var(--fd-sidebar-width) + var(--fd-layout-offset))"},children:a.children})})})}function y({toc:a,children:b,...c}){let{collapsed:e}=(0,q.c)();return(0,d.jsx)(r.W,{...a,children:(0,d.jsx)("div",{id:"nd-page",...c,className:(0,h.QP)("flex flex-1 mx-auto w-full",c.className),style:{paddingTop:"calc(var(--fd-nav-height) + var(--fd-tocnav-height))",maxWidth:e?"var(--fd-page-width)":"min(var(--fd-page-width),calc(var(--fd-layout-width) - var(--fd-sidebar-width)))",...c.style},children:b})})}function z({date:a,...b}){let{text:c}=(0,i.useI18n)(),[f,g]=(0,e.useState)("");return(0,d.jsxs)("p",{...b,className:(0,h.QP)("text-sm text-fd-muted-foreground",b.className),children:[c.lastUpdate," ",f]})}let A=new WeakMap;function B({items:a,...b}){let{root:c}=(0,j.t)(),f=(0,k.a8)(),{previous:g,next:i}=(0,e.useMemo)(()=>{if(a)return a;let b=A.get(c)??function a(b){let c=[];return b.forEach(b=>{if("folder"===b.type){b.index&&c.push(b.index),c.push(...a(b.children));return}"page"!==b.type||b.external||c.push(b)}),c}(c.children);A.set(c,b);let d=b.findIndex(a=>(0,n.$)(a.url,f,!1));return -1===d?{}:{previous:b[d-1],next:b[d+1]}},[a,f,c]);return(0,d.jsxs)("div",{...b,className:(0,h.QP)("@container grid gap-4 pb-6",g&&i?"grid-cols-2":"grid-cols-1",b.className),children:[g?(0,d.jsx)(C,{item:g,index:0}):null,i?(0,d.jsx)(C,{item:i,index:1}):null]})}function C({item:a,index:b}){let{text:c}=(0,i.useI18n)(),e=0===b?f.JG:f.c_;return(0,d.jsxs)(g.default,{href:a.url,className:(0,h.QP)("flex flex-col gap-2 rounded-lg border p-4 text-sm transition-colors hover:bg-fd-accent/80 hover:text-fd-accent-foreground @max-lg:col-span-full",1===b&&"text-end"),children:[(0,d.jsxs)("div",{className:(0,h.QP)("inline-flex items-center gap-1.5 font-medium",1===b&&"flex-row-reverse"),children:[(0,d.jsx)(e,{className:"-mx-1 size-4 shrink-0 rtl:rotate-180"}),(0,d.jsx)("p",{children:a.name})]}),(0,d.jsx)("p",{className:"text-fd-muted-foreground truncate",children:a.description??(0===b?c.previousPage:c.nextPage)})]})}function D({includeRoot:a=!1,includeSeparator:b,includePage:c=!1,...f}){let i=(0,j.L)(),{root:k}=(0,j.t)(),m=(0,e.useMemo)(()=>(0,l.Pp)(k,i,{includePage:c,includeSeparator:b,includeRoot:a}),[c,a,b,i,k]);return 0===m.length?null:(0,d.jsx)("div",{...f,className:(0,h.QP)("flex items-center gap-1.5 text-sm text-fd-muted-foreground",f.className),children:m.map((a,b)=>{let c=(0,h.QP)("truncate",b===m.length-1&&"text-fd-primary font-medium");return(0,d.jsxs)(e.Fragment,{children:[0!==b&&(0,d.jsx)("span",{className:"text-fd-foreground/30",children:"/"}),a.url?(0,d.jsx)(g.default,{href:a.url,className:(0,h.QP)(c,"transition-opacity hover:opacity-80"),children:a.name}):(0,d.jsx)("span",{className:c,children:a.name})]},b)})})}function E(a){return(0,d.jsx)("div",{id:"nd-toc",...a,className:(0,h.QP)("sticky pb-2 pt-12 max-xl:hidden",a.className),style:{...a.style,top:"calc(var(--fd-banner-height) + var(--fd-nav-height))",height:"calc(100dvh - var(--fd-banner-height) - var(--fd-nav-height))"},children:(0,d.jsx)("div",{className:"flex h-full w-(--fd-toc-width) max-w-full flex-col pe-4",children:a.children})})}},38523:(a,b)=>{"use strict";function c(a){let{ampFirst:b=!1,hybrid:c=!1,hasQuery:d=!1}=void 0===a?{}:a;return b||c&&d}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isInAmpMode",{enumerable:!0,get:function(){return c}})},38689:(a,b,c)=>{"use strict";c.d(b,{default:()=>l});var d=c(13486),e=c(48878),f=c(60159),g=c(91518),h=c(35476),i=c(78041),j=c(85624),k=c(9650);function l({ref:a,className:b,...c}){let e=(0,f.useRef)(null),l=(0,i.a)(),{text:n}=(0,k.useI18n)(),[o,p]=(0,f.useState)();return 0===l.length?(0,d.jsx)("div",{className:"rounded-lg border bg-fd-card p-3 text-xs text-fd-muted-foreground",children:n.tocNoHeadings}):(0,d.jsxs)(d.Fragment,{children:[o?(0,d.jsx)("div",{className:"absolute start-0 top-0 rtl:-scale-x-100",style:{width:o.width,height:o.height,maskImage:`url("data:image/svg+xml,${encodeURIComponent(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${o.width} ${o.height}"><path d="${o.path}" stroke="black" stroke-width="1" fill="none" /></svg>`)}")`},children:(0,d.jsx)(h.j,{containerRef:e,className:"mt-(--fd-top) h-(--fd-height) bg-fd-primary transition-all"})}):null,(0,d.jsx)("div",{ref:(0,j.P)(e,a),className:(0,g.QP)("flex flex-col",b),...c,children:l.map((a,b)=>(0,d.jsx)(m,{item:a,upper:l[b-1]?.depth,lower:l[b+1]?.depth},a.url))})]})}function m({item:a,upper:b=a.depth,lower:c=a.depth}){var f;let h=10*(a.depth>=3),i=10*(b>=3);return(0,d.jsxs)(e.Cz,{href:a.url,style:{paddingInlineStart:(f=a.depth)<=2?14:3===f?26:36},className:"prose relative py-1.5 text-sm text-fd-muted-foreground hover:text-fd-accent-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary",children:[h!==i?(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",className:"absolute -top-1.5 start-0 size-4 rtl:-scale-x-100",children:(0,d.jsx)("line",{x1:i,y1:"0",x2:h,y2:"12",className:"stroke-fd-foreground/10",strokeWidth:"1"})}):null,(0,d.jsx)("div",{className:(0,g.QP)("absolute inset-y-0 w-px bg-fd-foreground/10",h!==i&&"top-1.5",h!==10*(c>=3)&&"bottom-1.5"),style:{insetInlineStart:h}}),a.title]})}},40356:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>y,generateMetadata:()=>A,generateStaticParams:()=>z});var d=c(38828),e=c(2747);let f=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,g=Object.hasOwnProperty;class h{constructor(){this.occurrences,this.reset()}slug(a,b){var c,d;let e=(c=a,d=!0===b,"string"!=typeof c?"":(d||(c=c.toLowerCase()),c.replace(f,"").replace(/ /g,"-"))),h=e;for(;g.call(this.occurrences,e);)this.occurrences[h]++,e=h+"-"+this.occurrences[h];return this.occurrences[e]=0,e}reset(){this.occurrences=Object.create(null)}}new h,Object.create,Object.getOwnPropertyDescriptor,Object.getOwnPropertyNames,Object.getPrototypeOf,Object.prototype.hasOwnProperty,c(71896),require("node:path");var i=c(61365),j=c(8512),k=c(16499),l=c(73187),m=c(57544),n=c(95284),o=c(77795),p=c(57999);function q(a){return(0,d.jsxs)("h3",{...a,className:(0,j.QP)("inline-flex items-center gap-1.5 text-sm text-fd-muted-foreground",a.className),children:[(0,d.jsx)(l.EY,{className:"size-4"}),(0,d.jsx)(m.I18nLabel,{label:"toc"})]})}function r({variant:a="normal",...b}){return(0,d.jsx)(o.TOCScrollArea,{...b,children:"clerk"===a?(0,d.jsx)(p.default,{}):(0,d.jsx)(o.TOCItems,{})})}function s({variant:a="normal",...b}){return(0,d.jsx)(o.TOCScrollArea,{...b,children:"clerk"===a?(0,d.jsx)(p.default,{}):(0,d.jsx)(o.TOCItems,{})})}function t(a){return(0,d.jsx)("article",{...a,className:(0,j.QP)("flex min-w-0 w-full flex-col gap-4 px-4 pt-8 md:px-6 md:mx-auto xl:pt-12 xl:px-12",a.className),children:a.children})}function u({editOnGithub:a,breadcrumb:{enabled:b=!0,component:c,...e}={},footer:f={},lastUpdate:g,container:h,full:i=!1,tableOfContentPopover:{enabled:j,component:k,...l}={},tableOfContent:{enabled:m,component:o,...p}={},toc:u=[],article:w,children:x}){let y=u.length>0||void 0!==p.footer||void 0!==p.header;return m??(m=!i&&y),j??(j=u.length>0||void 0!==l.header||void 0!==l.footer),(0,d.jsxs)(n.PageRoot,{toc:{toc:u,single:p.single},...h,children:[j&&(k??(0,d.jsxs)(n.PageTOCPopover,{children:[(0,d.jsx)(n.PageTOCPopoverTrigger,{}),(0,d.jsxs)(n.PageTOCPopoverContent,{children:[l.header,(0,d.jsx)(s,{variant:l.style}),l.footer]})]})),(0,d.jsxs)(t,{...w,children:[b&&(c??(0,d.jsx)(n.PageBreadcrumb,{...e})),x,(0,d.jsx)("div",{role:"none",className:"flex-1"}),(0,d.jsxs)("div",{className:"flex flex-row flex-wrap items-center justify-between gap-4 empty:hidden",children:[a&&(0,d.jsx)(v,{href:`https://github.com/${a.owner}/${a.repo}/blob/${a.sha}/${a.path.startsWith("/")?a.path.slice(1):a.path}`}),g&&(0,d.jsx)(n.PageLastUpdate,{date:new Date(g)})]}),!1!==f.enabled&&(f.component??(0,d.jsx)(n.PageFooter,{items:f.items}))]}),m&&(o??(0,d.jsxs)(n.PageTOC,{children:[p.header,(0,d.jsx)(q,{}),(0,d.jsx)(r,{variant:p.style}),p.footer]}))]})}function v(a){return(0,d.jsx)("a",{target:"_blank",rel:"noreferrer noopener",...a,className:(0,j.QP)((0,k.r)({color:"secondary",size:"sm",className:"gap-1.5 not-prose"}),a.className),children:a.children??(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(l.ff,{className:"size-3.5"}),(0,d.jsx)(m.I18nLabel,{label:"editOnGithub"})]})})}let w=(0,i.forwardRef)((a,b)=>(0,d.jsx)("div",{ref:b,...a,className:(0,j.QP)("prose",a.className),children:a.children}));w.displayName="DocsBody",(0,i.forwardRef)((a,b)=>void 0===a.children?null:(0,d.jsx)("p",{ref:b,...a,className:(0,j.QP)("mb-8 text-lg text-fd-muted-foreground",a.className),children:a.children})).displayName="DocsDescription",(0,i.forwardRef)((a,b)=>(0,d.jsx)("h1",{ref:b,...a,className:(0,j.QP)("text-3xl font-semibold",a.className),children:a.children})).displayName="DocsTitle";var x=c(42543);async function y({params:a}){let{slug:b}=await a,c=e.s.getPage(b||["index"]);c||(0,x.notFound)();let f=c.data.body;return(0,d.jsx)(u,{toc:c.data.toc,full:c.data.full,children:(0,d.jsxs)(w,{children:[(0,d.jsx)("h1",{children:c.data.title}),(0,d.jsx)(f,{})]})})}async function z(){return e.s.generateParams()}async function A({params:a}){let{slug:b}=await a,c=e.s.getPage(b||["index"]);return c?{title:c.data.title,description:c.data.description}:{}}c(21971),c(37087)},40480:(a,b,c)=>{"use strict";c.d(b,{RootToggle:()=>m});var d=c(13486),e=c(19037),f=c(60159),g=c(55703),h=c(86663),i=c(91518),j=c(86232),k=c(21944),l=c(95670);function m({options:a,placeholder:b,...c}){let[m,n]=(0,f.useState)(!1),{closeOnRedirect:o}=(0,k.c)(),p=(0,h.a8)(),q=(0,f.useMemo)(()=>{let b=p.endsWith("/")?p.slice(0,-1):p;return a.findLast(a=>a.urls?a.urls.has(b):(0,j.$)(a.url,p,!0))},[a,p]),r=()=>{o.current=!1,n(!1)},s=q?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"size-9 md:size-5",children:q.icon}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:q.title}),(0,d.jsx)("p",{className:"text-[13px] text-fd-muted-foreground empty:hidden md:hidden",children:q.description})]})]}):b;return(0,d.jsxs)(l.AM,{open:m,onOpenChange:n,children:[s&&(0,d.jsxs)(l.Wv,{...c,className:(0,i.QP)("flex items-center gap-2 rounded-lg p-2 border bg-fd-secondary/50 text-start text-fd-secondary-foreground transition-colors hover:bg-fd-accent data-[state=open]:bg-fd-accent data-[state=open]:text-fd-accent-foreground",c.className),children:[s,(0,d.jsx)(e.Ml,{className:"ms-auto size-4 text-fd-muted-foreground"})]}),(0,d.jsx)(l.hl,{className:"flex flex-col gap-1 min-w-(--radix-popover-trigger-width) overflow-hidden p-1",children:a.map(a=>{let b=a===q;return(0,d.jsxs)(g.default,{href:a.url,onClick:r,...a.props,className:(0,i.QP)("flex items-center gap-2 rounded-lg p-1.5 hover:bg-fd-accent hover:text-fd-accent-foreground",a.props?.className),children:[(0,d.jsx)("div",{className:"size-9 md:size-5 md:mt-1 md:mb-auto",children:a.icon}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:a.title}),(0,d.jsx)("p",{className:"text-[13px] text-fd-muted-foreground empty:hidden",children:a.description})]}),(0,d.jsx)(e.Jl,{className:(0,i.QP)("ms-auto size-3.5 text-fd-primary",!b&&"invisible")})]},a.url)})})]})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42543:(a,b,c)=>{"use strict";var d=c(68855);c.o(d,"notFound")&&c.d(b,{notFound:function(){return d.notFound}}),c.o(d,"redirect")&&c.d(b,{redirect:function(){return d.redirect}})},44382:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return i},getImageProps:function(){return h}});let d=c(50686),e=c(69356),f=c(75636),g=d._(c(92122));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},44989:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return i}});let d=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=e(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(d,g,h):d[g]=a[g]}return d.default=a,c&&c.set(a,d),d}(c(61365));function e(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(e=function(a){return a?c:b})(a)}let f={current:null},g="function"==typeof d.cache?d.cache:a=>a,h=console.warn;function i(a){return function(...b){h(a(...b))}}g(a=>{try{h(f.current)}finally{f.current=null}})},46184:(a,b,c)=>{"use strict";c.r(b),c.d(b,{Sidebar:()=>x,SidebarCollapseTrigger:()=>J,SidebarContent:()=>y,SidebarContentMobile:()=>z,SidebarFolder:()=>F,SidebarFolderContent:()=>I,SidebarFolderLink:()=>H,SidebarFolderTrigger:()=>G,SidebarFooter:()=>B,SidebarHeader:()=>A,SidebarItem:()=>E,SidebarPageTree:()=>M,SidebarSeparator:()=>D,SidebarViewport:()=>C});var d=c(13486),e=c(19037),f=c(86663),g=c(60159),h=c(55703),i=c(59492),j=c(91518),k=c(82e3);let l=g.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(k.Root,{ref:e,type:"scroll",className:(0,j.QP)("overflow-hidden",a),...c,children:[b,(0,d.jsx)(k.Corner,{}),(0,d.jsx)(n,{orientation:"vertical"})]}));l.displayName=k.Root.displayName;let m=g.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsx)(k.Viewport,{ref:e,className:(0,j.QP)("size-full rounded-[inherit]",a),...c,children:b}));m.displayName=k.Viewport.displayName;let n=g.forwardRef(({className:a,orientation:b="vertical",...c},e)=>(0,d.jsx)(k.Scrollbar,{ref:e,orientation:b,className:(0,j.QP)("flex select-none data-[state=hidden]:animate-fd-fade-out","vertical"===b&&"h-full w-1.5","horizontal"===b&&"h-1.5 flex-col",a),...c,children:(0,d.jsx)(k.ScrollAreaThumb,{className:"relative flex-1 rounded-full bg-fd-border"})}));n.displayName=k.Scrollbar.displayName;var o=c(86232),p=c(93687),q=c(21944),r=c(76353),s=c(19366);c(68926);var t=c(78998);let u=(0,r.F)("relative flex flex-row items-center gap-2 rounded-xl p-2 ps-(--sidebar-item-offset) text-start text-fd-muted-foreground [overflow-wrap:anywhere] [&_svg]:size-4 [&_svg]:shrink-0",{variants:{active:{true:"bg-fd-primary/10 text-fd-primary",false:"transition-colors hover:bg-fd-accent/50 hover:text-fd-accent-foreground/80 hover:transition-none"}}}),v=(0,g.createContext)(null),w=(0,g.createContext)(null);function x({defaultOpenLevel:a=0,prefetch:b=!0,Mobile:c,Content:e}){let f=function(a,b=!1){let[c,d]=(0,g.useState)(null);return(0,g.useEffect)(()=>{if(b)return;let c=window.matchMedia(a),e=()=>{d(c.matches)};return e(),c.addEventListener("change",e),()=>{c.removeEventListener("change",e)}},[b,a]),c}("(width < 768px)")??!1,h=(0,g.useMemo)(()=>({defaultOpenLevel:a,prefetch:b,level:1}),[a,b]);return(0,d.jsx)(v.Provider,{value:h,children:f&&null!=c?c:e})}function y(a){let{collapsed:b}=(0,q.c)(),[c,e]=(0,g.useState)(!1),f=(0,g.useRef)(0),h=(0,g.useRef)(0);return(0,i.T)(b,()=>{e(!1),h.current=Date.now()+150}),(0,d.jsx)("aside",{id:"nd-sidebar",...a,"data-collapsed":b,className:(0,j.QP)("fixed start-0 flex flex-col items-end top-(--fd-sidebar-top) bottom-(--fd-sidebar-margin) z-20 bg-fd-card text-sm border-e max-md:hidden *:w-(--fd-sidebar-width)",b&&["rounded-xl border translate-x-(--fd-sidebar-offset) rtl:-translate-x-(--fd-sidebar-offset)",c?"z-50 shadow-lg":"opacity-0"],a.className),style:{transition:["top","opacity","translate","width"].map(a=>`${a} ease 250ms`).join(", "),...a.style,"--fd-sidebar-offset":c?"calc(var(--spacing) * 2)":"calc(16px - 100%)","--fd-sidebar-margin":b?"0.5rem":"0px","--fd-sidebar-top":"calc(var(--fd-banner-height) + var(--fd-nav-height) + var(--fd-sidebar-margin))",width:b?"var(--fd-sidebar-width)":"calc(var(--fd-sidebar-width) + var(--fd-layout-offset))"},onPointerEnter:a=>{!b||"touch"===a.pointerType||h.current>Date.now()||(window.clearTimeout(f.current),e(!0))},onPointerLeave:a=>{b&&"touch"!==a.pointerType&&(window.clearTimeout(f.current),f.current=window.setTimeout(()=>{e(!1),h.current=Date.now()+150},Math.min(a.clientX,document.body.clientWidth-a.clientX)>100?0:500))},children:a.children})}function z({className:a,children:b,...c}){let{open:e,setOpen:f}=(0,q.c)(),g=e?"open":"closed";return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(t.C,{present:e,children:(0,d.jsx)("div",{"data-state":g,className:"fixed z-40 inset-0 backdrop-blur-xs data-[state=open]:animate-fd-fade-in data-[state=closed]:animate-fd-fade-out",onClick:()=>f(!1)})}),(0,d.jsx)(t.C,{present:e,children:({present:e})=>(0,d.jsx)("aside",{id:"nd-sidebar-mobile",...c,"data-state":g,className:(0,j.QP)("fixed text-[15px] flex flex-col shadow-lg border-s end-0 inset-y-0 w-[85%] max-w-[380px] z-40 bg-fd-background data-[state=open]:animate-fd-sidebar-in data-[state=closed]:animate-fd-sidebar-out",!e&&"invisible",a),children:b})})]})}function A(a){return(0,d.jsx)("div",{...a,className:(0,j.QP)("flex flex-col gap-3 p-4 pb-2",a.className),children:a.children})}function B(a){return(0,d.jsx)("div",{...a,className:(0,j.QP)("flex flex-col border-t px-4 py-3",a.className),children:a.children})}function C(a){return(0,d.jsx)(l,{...a,className:(0,j.QP)("h-full",a.className),children:(0,d.jsx)(m,{className:"p-4",style:{"--sidebar-item-offset":"calc(var(--spacing) * 2)",maskImage:"linear-gradient(to bottom, transparent, white 12px, white calc(100% - 12px), transparent)"},children:a.children})})}function D(a){return(0,d.jsx)("p",{...a,className:(0,j.QP)("inline-flex items-center gap-2 mb-1.5 px-2 ps-(--sidebar-item-offset) empty:mb-0 [&_svg]:size-4 [&_svg]:shrink-0",a.className),children:a.children})}function E({icon:a,...b}){let c=(0,f.a8)(),g=void 0!==b.href&&(0,o.$)(b.href,c,!1),{prefetch:i}=L();return(0,d.jsxs)(h.default,{...b,"data-active":g,className:(0,j.QP)(u({active:g}),b.className),prefetch:i,children:[a??(b.external?(0,d.jsx)(e.Gr,{}):null),b.children]})}function F({defaultOpen:a=!1,...b}){let[c,e]=(0,g.useState)(a);return(0,i.T)(a,a=>{a&&e(a)}),(0,d.jsx)(p.Nt,{open:c,onOpenChange:e,...b,children:(0,d.jsx)(w.Provider,{value:(0,g.useMemo)(()=>({open:c,setOpen:e}),[c]),children:b.children})})}function G({className:a,...b}){let{open:c}=K();return(0,d.jsxs)(p.R6,{className:(0,j.QP)(u({active:!1}),"w-full",a),...b,children:[b.children,(0,d.jsx)(e.yQ,{"data-icon":!0,className:(0,j.QP)("ms-auto transition-transform",!c&&"-rotate-90")})]})}function H(a){let{open:b,setOpen:c}=K(),{prefetch:g}=L(),i=(0,f.a8)(),k=void 0!==a.href&&(0,o.$)(a.href,i,!1);return(0,d.jsxs)(h.default,{...a,"data-active":k,className:(0,j.QP)(u({active:k}),"w-full",a.className),onClick:a=>{a.target instanceof HTMLElement&&a.target.hasAttribute("data-icon")?(c(!b),a.preventDefault()):c(!k||!b)},prefetch:g,children:[a.children,(0,d.jsx)(e.yQ,{"data-icon":!0,className:(0,j.QP)("ms-auto transition-transform",!b&&"-rotate-90")})]})}function I(a){let b=L(),c=b.level+1;return(0,d.jsxs)(p.Ke,{...a,className:(0,j.QP)("relative",2===c&&"**:data-[active=true]:before:content-[''] **:data-[active=true]:before:bg-fd-primary **:data-[active=true]:before:absolute **:data-[active=true]:before:w-px **:data-[active=true]:before:inset-y-2.5 **:data-[active=true]:before:start-2.5",a.className),style:{"--sidebar-item-offset":`calc(var(--spacing) * ${c>1?3*c:2})`,...a.style},children:[2===c&&(0,d.jsx)("div",{className:"absolute w-px inset-y-1 bg-fd-border start-2.5"}),(0,d.jsx)(v.Provider,{value:(0,g.useMemo)(()=>({...b,level:c}),[b,c]),children:a.children})]})}function J(a){let{collapsed:b,setCollapsed:c}=(0,q.c)();return(0,d.jsx)("button",{type:"button","aria-label":"Collapse Sidebar","data-collapsed":b,...a,onClick:()=>{c(a=>!a)},children:a.children})}function K(){let a=(0,g.useContext)(w);if(!a)throw Error("Missing sidebar folder");return a}function L(){let a=(0,g.useContext)(v);if(!a)throw Error("<Sidebar /> component required.");return a}function M(a){let{root:b}=(0,s.t)();return(0,g.useMemo)(()=>{let{Separator:c,Item:e,Folder:f}=a.components??{};return(0,d.jsx)(g.Fragment,{children:function a(b,g){return b.map((b,h)=>{if("separator"===b.type)return c?(0,d.jsx)(c,{item:b},h):(0,d.jsxs)(D,{className:(0,j.QP)(0!==h&&"mt-6"),children:[b.icon,b.name]},h);if("folder"===b.type){let c=a(b.children,g+1);return f?(0,d.jsx)(f,{item:b,level:g,children:c},h):(0,d.jsx)(N,{item:b,children:c},h)}return e?(0,d.jsx)(e,{item:b},b.url):(0,d.jsx)(E,{href:b.url,external:b.external,icon:b.icon,children:b.name},b.url)})}(b.children,1)},b.$id)},[a.components,b])}function N({item:a,...b}){let{defaultOpenLevel:c,level:e}=L(),f=(0,s.L)();return(0,d.jsxs)(F,{defaultOpen:(a.defaultOpen??c>=e)||f.includes(a),children:[a.index?(0,d.jsxs)(H,{href:a.index.url,external:a.index.external,...b,children:[a.icon,a.name]}):(0,d.jsxs)(G,{...b,children:[a.icon,a.name]}),(0,d.jsx)(I,{children:b.children})]})}},46964:(a,b,c)=>{"use strict";c.d(b,{TreeContextProvider:()=>e});var d=c(66352);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call TreeContextProvider() from the server but TreeContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/tree.js","TreeContextProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useTreePath() from the server but useTreePath is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/tree.js","useTreePath"),(0,d.registerClientReference)(function(){throw Error("Attempted to call useTreeContext() from the server but useTreeContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/tree.js","useTreeContext")},48878:(a,b,c)=>{"use strict";c.d(b,{Cz:()=>n,Mf:()=>k,N2:()=>l,NQ:()=>m,R3:()=>j});var d=c(99156);c(68926);var e=c(60159),f=c(55115),g=c(13486),h=(0,e.createContext)([]),i=(0,e.createContext)({current:null});function j(){return(0,e.useContext)(h).at(-1)}function k(){return(0,e.useContext)(h)}function l({containerRef:a,children:b}){return(0,g.jsx)(i.Provider,{value:a,children:b})}function m({toc:a,single:b=!0,children:c}){return(0,e.useMemo)(()=>a.map(a=>a.url.split("#")[1]),[a]),(0,g.jsx)(h.Provider,{value:function(a,b){let[c,d]=(0,e.useState)([]);return b?c.slice(0,1):c}(0,b),children:c})}var n=(0,e.forwardRef)(({onActiveChange:a,...b},c)=>{let h=(0,e.useContext)(i),j=k(),l=(0,e.useRef)(null),m=function(...a){return b=>{a.forEach(a=>{"function"==typeof a?a(b):null!==a&&(a.current=b)})}}(l,c),n=j.includes(b.href.slice(1));return(0,d.T)(n,b=>{let c=l.current;c&&(b&&h.current&&(0,f.A)(c,{behavior:"smooth",block:"center",inline:"center",scrollMode:"always",boundary:h.current}),a?.(b))}),(0,g.jsx)("a",{ref:m,"data-active":n,...b,children:b.children})});n.displayName="TOCItem"},49600:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=c(49005),e=c(31903),f=c(19121).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},50298:(a,b,c)=>{"use strict";a.exports=c(69358).vendored.contexts.ImageConfigContext},50872:(a,b,c)=>{"use strict";a.exports=c(69358).vendored.contexts.HeadManagerContext},51820:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{VALID_LOADERS:function(){return c},imageConfigDefault:function(){return d}});let c=["default","imgix","cloudinary","akamai","custom"],d={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},51943:(a,b,c)=>{"use strict";c.d(b,{ThemeToggle:()=>l});var d=c(13486),e=c(76353),f=c(19037),g=c(48961),h=c(60159),i=c(91518);let j=(0,e.F)("size-6.5 rounded-full p-1.5 text-fd-muted-foreground",{variants:{active:{true:"bg-fd-accent text-fd-accent-foreground",false:"text-fd-muted-foreground"}}}),k=[["light",f.bd],["dark",f.AX],["system",f.$3]];function l({className:a,mode:b="light-dark",...c}){let{setTheme:e,theme:f,resolvedTheme:l}=(0,g.D)(),[m,n]=(0,h.useState)(!1),o=(0,i.QP)("inline-flex items-center rounded-full border p-1",a);if("light-dark"===b){let a=m?l:null;return(0,d.jsx)("button",{className:o,"aria-label":"Toggle Theme",onClick:()=>e("light"===a?"dark":"light"),"data-theme-toggle":"",...c,children:k.map(([b,c])=>{if("system"!==b)return(0,d.jsx)(c,{fill:"currentColor",className:(0,i.QP)(j({active:a===b}))},b)})})}let p=m?f:null;return(0,d.jsx)("div",{className:o,"data-theme-toggle":"",...c,children:k.map(([a,b])=>(0,d.jsx)("button",{"aria-label":a,className:(0,i.QP)(j({active:p===a})),onClick:()=>e(a),children:(0,d.jsx)(b,{className:"size-full",fill:"currentColor"})},a))})}},53720:(a,b,c)=>{"use strict";c.d(b,{l:()=>g,q:()=>f});var d=c(38836),e=c(81604);let f=new d.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(a,b)=>!(b?.status>=400&&b?.status<500)&&a<3,refetchOnWindowFocus:!1,throwOnError:!1},mutations:{retry:!1,onError:a=>{console.error("Mutation error:",a);let b=a?.message||"An error occurred";e.oR.error(b)}}}}),g={rules:{all:["rules"],lists:()=>[...g.rules.all,"list"],list:a=>[...g.rules.lists(),a],details:()=>[...g.rules.all,"detail"],detail:a=>[...g.rules.details(),a],raw:a=>[...g.rules.all,"raw",a]},rulesets:{all:["rulesets"],lists:()=>[...g.rulesets.all,"list"],list:a=>[...g.rulesets.lists(),a],details:()=>[...g.rulesets.all,"detail"],detail:a=>[...g.rulesets.details(),a]},tags:{all:["tags"],lists:()=>[...g.tags.all,"list"]},user:{all:["user"],profile:a=>[...g.user.all,"profile",a]}}},55115:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});let d=a=>"object"==typeof a&&null!=a&&1===a.nodeType,e=(a,b)=>(!b||"hidden"!==a)&&"visible"!==a&&"clip"!==a,f=(a,b)=>{if(a.clientHeight<a.scrollHeight||a.clientWidth<a.scrollWidth){let c=getComputedStyle(a,null);return e(c.overflowY,b)||e(c.overflowX,b)||(a=>{let b=(a=>{if(!a.ownerDocument||!a.ownerDocument.defaultView)return null;try{return a.ownerDocument.defaultView.frameElement}catch(a){return null}})(a);return!!b&&(b.clientHeight<a.scrollHeight||b.clientWidth<a.scrollWidth)})(a)}return!1},g=(a,b,c,d,e,f,g,h)=>f<a&&g>b||f>a&&g<b?0:f<=a&&h<=c||g>=b&&h>=c?f-a-d:g>b&&h<c||f<a&&h>c?g-b+e:0,h=a=>{let b=a.parentElement;return null==b?a.getRootNode().host||null:b},i=(a,b)=>{var c,e,i,j;if("undefined"==typeof document)return[];let{scrollMode:k,block:l,inline:m,boundary:n,skipOverflowHiddenElements:o}=b,p="function"==typeof n?n:a=>a!==n;if(!d(a))throw TypeError("Invalid target");let q=document.scrollingElement||document.documentElement,r=[],s=a;for(;d(s)&&p(s);){if((s=h(s))===q){r.push(s);break}null!=s&&s===document.body&&f(s)&&!f(document.documentElement)||null!=s&&f(s,o)&&r.push(s)}let t=null!=(e=null==(c=window.visualViewport)?void 0:c.width)?e:innerWidth,u=null!=(j=null==(i=window.visualViewport)?void 0:i.height)?j:innerHeight,{scrollX:v,scrollY:w}=window,{height:x,width:y,top:z,right:A,bottom:B,left:C}=a.getBoundingClientRect(),{top:D,right:E,bottom:F,left:G}=(a=>{let b=window.getComputedStyle(a);return{top:parseFloat(b.scrollMarginTop)||0,right:parseFloat(b.scrollMarginRight)||0,bottom:parseFloat(b.scrollMarginBottom)||0,left:parseFloat(b.scrollMarginLeft)||0}})(a),H="start"===l||"nearest"===l?z-D:"end"===l?B+F:z+x/2-D+F,I="center"===m?C+y/2-G+E:"end"===m?A+E:C-G,J=[];for(let a=0;a<r.length;a++){let b=r[a],{height:c,width:d,top:e,right:h,bottom:i,left:j}=b.getBoundingClientRect();if("if-needed"===k&&z>=0&&C>=0&&B<=u&&A<=t&&(b===q&&!f(b)||z>=e&&B<=i&&C>=j&&A<=h))break;let n=getComputedStyle(b),o=parseInt(n.borderLeftWidth,10),p=parseInt(n.borderTopWidth,10),s=parseInt(n.borderRightWidth,10),D=parseInt(n.borderBottomWidth,10),E=0,F=0,G="offsetWidth"in b?b.offsetWidth-b.clientWidth-o-s:0,K="offsetHeight"in b?b.offsetHeight-b.clientHeight-p-D:0,L="offsetWidth"in b?0===b.offsetWidth?0:d/b.offsetWidth:0,M="offsetHeight"in b?0===b.offsetHeight?0:c/b.offsetHeight:0;if(q===b)E="start"===l?H:"end"===l?H-u:"nearest"===l?g(w,w+u,u,p,D,w+H,w+H+x,x):H-u/2,F="start"===m?I:"center"===m?I-t/2:"end"===m?I-t:g(v,v+t,t,o,s,v+I,v+I+y,y),E=Math.max(0,E+w),F=Math.max(0,F+v);else{E="start"===l?H-e-p:"end"===l?H-i+D+K:"nearest"===l?g(e,i,c,p,D+K,H,H+x,x):H-(e+c/2)+K/2,F="start"===m?I-j-o:"center"===m?I-(j+d/2)+G/2:"end"===m?I-h+s+G:g(j,h,d,o,s+G,I,I+y,y);let{scrollLeft:a,scrollTop:f}=b;E=0===M?0:Math.max(0,Math.min(f+E/M,b.scrollHeight-c/M+K)),F=0===L?0:Math.max(0,Math.min(a+F/L,b.scrollWidth-d/L+G)),H+=f-E,I+=a-F}J.push({el:b,top:E,left:F})}return J};function j(a,b){if(!a.isConnected||!(a=>{let b=a;for(;b&&b.parentNode;){if(b.parentNode===document)return!0;b=b.parentNode instanceof ShadowRoot?b.parentNode.host:b.parentNode}return!1})(a))return;let c=(a=>{let b=window.getComputedStyle(a);return{top:parseFloat(b.scrollMarginTop)||0,right:parseFloat(b.scrollMarginRight)||0,bottom:parseFloat(b.scrollMarginBottom)||0,left:parseFloat(b.scrollMarginLeft)||0}})(a);if("object"==typeof b&&"function"==typeof b.behavior)return b.behavior(i(a,b));let d="boolean"==typeof b||null==b?void 0:b.behavior;for(let{el:e,top:f,left:g}of i(a,!1===b?{block:"end",inline:"nearest"}:b===Object(b)&&0!==Object.keys(b).length?b:{block:"start",inline:"nearest"})){let a=f-c.top+c.bottom,b=g-c.left+c.right;e.scroll({top:a,left:b,behavior:d})}}},55703:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(28186),e=c(60159),f=c(13486),g=(0,e.forwardRef)(({href:a="#",external:b=!(a.startsWith("/")||a.startsWith("#")||a.startsWith(".")),prefetch:c,...e},g)=>b?(0,f.jsx)("a",{ref:g,href:a,rel:"noreferrer noopener",target:"_blank",...e,children:e.children}):(0,f.jsx)(d.N_,{ref:g,href:a,prefetch:c,...e}));g.displayName="Link",c(68926)},57419:(a,b)=>{"use strict";function c(a){let{widthInt:b,heightInt:c,blurWidth:d,blurHeight:e,blurDataURL:f,objectFit:g}=a,h=d?40*d:b,i=e?40*e:c,j=h&&i?"viewBox='0 0 "+h+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+j+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(j?"none":"contain"===g?"xMidYMid":"cover"===g?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+f+"'/%3E%3C/svg%3E"}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImageBlurSvg",{enumerable:!0,get:function(){return c}})},57544:(a,b,c)=>{"use strict";c.r(b),c.d(b,{I18nContext:()=>f,I18nLabel:()=>g,defaultTranslations:()=>e,useI18n:()=>h});var d=c(66352);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call defaultTranslations() from the server but defaultTranslations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/i18n.js","defaultTranslations"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call I18nContext() from the server but I18nContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/i18n.js","I18nContext"),g=(0,d.registerClientReference)(function(){throw Error("Attempted to call I18nLabel() from the server but I18nLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/i18n.js","I18nLabel"),h=(0,d.registerClientReference)(function(){throw Error("Attempted to call useI18n() from the server but useI18n is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/i18n.js","useI18n")},57999:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js","default")},59492:(a,b,c)=>{"use strict";c.d(b,{T:()=>d.T});var d=c(99156);c(68926)},60026:(a,b,c)=>{Promise.resolve().then(c.bind(c,60572)),Promise.resolve().then(c.bind(c,55703)),Promise.resolve().then(c.bind(c,23850)),Promise.resolve().then(c.bind(c,40480)),Promise.resolve().then(c.bind(c,1652)),Promise.resolve().then(c.bind(c,46184)),Promise.resolve().then(c.bind(c,51943)),Promise.resolve().then(c.bind(c,10640)),Promise.resolve().then(c.bind(c,19366)),Promise.resolve().then(c.bind(c,75062)),Promise.resolve().then(c.bind(c,76322)),Promise.resolve().then(c.bind(c,29958)),Promise.resolve().then(c.bind(c,10393)),Promise.resolve().then(c.bind(c,1934)),Promise.resolve().then(c.bind(c,15122))},60572:(a,b,c)=>{"use strict";c.d(b,{HideIfEmpty:()=>i}),c(68926);var d=c(60159),e=c(13486),f=(0,d.createContext)({nonce:void 0});function g(a){return document.querySelector(`[data-fd-if-empty="${a}"]`)}function h(a){for(let b=0;b<a.childNodes.length;b++){let c=a.childNodes.item(b);if(c.nodeType===Node.TEXT_NODE||c.nodeType===Node.ELEMENT_NODE&&"none"!==window.getComputedStyle(c).display)return!1}return!0}function i({as:a,...b}){let c=(0,d.useId)(),{nonce:i}=(0,d.useContext)(f),[j,k]=(0,d.useState)(()=>{});return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(a,{...b,"data-fd-if-empty":c,hidden:j??!1}),void 0===j&&(0,e.jsx)("script",{nonce:i,dangerouslySetInnerHTML:{__html:`{${g};${h};(${a=>{let b=g(a);b&&(b.hidden=h(b));let c=document.currentScript;c&&c.parentNode?.removeChild(c)}})("${c}")}`}})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63148:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l});var d=c(13486);c(60159);var e=c(3185),f=c(16918),g=c(99865),h=c(16692),i=c(45977),j=c(59203),k=c(39499);function l({error:a,reset:b}){return(0,d.jsx)("html",{children:(0,d.jsx)("body",{children:(0,d.jsx)(e.m,{size:"2",className:"min-h-screen flex items-center justify-center",children:(0,d.jsxs)(f.s,{direction:"column",align:"center",gap:"6",className:"text-center",children:[(0,d.jsx)(g.D,{size:"8",className:"text-5xl font-bold text-red-600",children:"Something went wrong!"}),(0,d.jsx)(h.E,{size:"4",color:"gray",className:"max-w-md",children:"We're sorry, but something unexpected happened. Please try refreshing the page or go back to the homepage."}),a.digest&&(0,d.jsxs)(h.E,{size:"2",color:"gray",className:"font-mono",children:["Error ID: ",a.digest]}),(0,d.jsxs)(f.s,{gap:"3",className:"mt-4",children:[(0,d.jsxs)(i.$,{variant:"solid",onClick:b,children:[(0,d.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Try again"]}),(0,d.jsx)(i.$,{variant:"outline",asChild:!0,children:(0,d.jsxs)("a",{href:"/",children:[(0,d.jsx)(k.A,{className:"mr-2 h-4 w-4"}),"Go Home"]})})]})]})})})})}},64122:(a,b,c)=>{Promise.resolve().then(c.bind(c,866))},68855:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_rethrow:function(){return i.unstable_rethrow}});let d=c(49600),e=c(31903),f=c(38050),g=c(29699),h=c(77670),i=c(69938);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},68926:(a,b,c)=>{"use strict";c.d(b,{P:()=>j,f:()=>k});var d=Object.create,e=Object.defineProperty,f=Object.getOwnPropertyDescriptor,g=Object.getOwnPropertyNames,h=Object.getPrototypeOf,i=Object.prototype.hasOwnProperty,j=(a,b)=>function(){return b||(0,a[g(a)[0]])((b={exports:{}}).exports,b),b.exports},k=(a,b,c)=>(c=null!=a?d(h(a)):{},((a,b,c,d)=>{if(b&&"object"==typeof b||"function"==typeof b)for(let h of g(b))i.call(a,h)||h===c||e(a,h,{get:()=>b[h],enumerable:!(d=f(b,h))||d.enumerable});return a})(!b&&a&&a.__esModule?c:e(c,"default",{value:a,enumerable:!0}),a))},69356:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImgProps",{enumerable:!0,get:function(){return i}}),c(12405);let d=c(57419),e=c(51820),f=["-moz-initial","fill","none","scale-down",void 0];function g(a){return void 0!==a.default}function h(a){return void 0===a?a:"number"==typeof a?Number.isFinite(a)?a:NaN:"string"==typeof a&&/^[0-9]+$/.test(a)?parseInt(a,10):NaN}function i(a,b){var c,i;let j,k,l,{src:m,sizes:n,unoptimized:o=!1,priority:p=!1,loading:q,className:r,quality:s,width:t,height:u,fill:v=!1,style:w,overrideSrc:x,onLoad:y,onLoadingComplete:z,placeholder:A="empty",blurDataURL:B,fetchPriority:C,decoding:D="async",layout:E,objectFit:F,objectPosition:G,lazyBoundary:H,lazyRoot:I,...J}=a,{imgConf:K,showAltText:L,blurComplete:M,defaultLoader:N}=b,O=K||e.imageConfigDefault;if("allSizes"in O)j=O;else{let a=[...O.deviceSizes,...O.imageSizes].sort((a,b)=>a-b),b=O.deviceSizes.sort((a,b)=>a-b),d=null==(c=O.qualities)?void 0:c.sort((a,b)=>a-b);j={...O,allSizes:a,deviceSizes:b,qualities:d}}if(void 0===N)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let P=J.loader||N;delete J.loader,delete J.srcSet;let Q="__next_img_default"in P;if(Q){if("custom"===j.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let a=P;P=b=>{let{config:c,...d}=b;return a(d)}}if(E){"fill"===E&&(v=!0);let a={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];a&&(w={...w,...a});let b={responsive:"100vw",fill:"100vw"}[E];b&&!n&&(n=b)}let R="",S=h(t),T=h(u);if((i=m)&&"object"==typeof i&&(g(i)||void 0!==i.src)){let a=g(m)?m.default:m;if(!a.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!a.height||!a.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(k=a.blurWidth,l=a.blurHeight,B=B||a.blurDataURL,R=a.src,!v)if(S||T){if(S&&!T){let b=S/a.width;T=Math.round(a.height*b)}else if(!S&&T){let b=T/a.height;S=Math.round(a.width*b)}}else S=a.width,T=a.height}let U=!p&&("lazy"===q||void 0===q);(!(m="string"==typeof m?m:R)||m.startsWith("data:")||m.startsWith("blob:"))&&(o=!0,U=!1),j.unoptimized&&(o=!0),Q&&!j.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(o=!0);let V=h(s),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:F,objectPosition:G}:{},L?{}:{color:"transparent"},w),X=M||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,d.getImageBlurSvg)({widthInt:S,heightInt:T,blurWidth:k,blurHeight:l,blurDataURL:B||"",objectFit:W.objectFit})+'")':'url("'+A+'")',Y=f.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(a){let{config:b,src:c,unoptimized:d,width:e,quality:f,sizes:g,loader:h}=a;if(d)return{src:c,srcSet:void 0,sizes:void 0};let{widths:i,kind:j}=function(a,b,c){let{deviceSizes:d,allSizes:e}=a;if(c){let a=/(^|\s)(1?\d?\d)vw/g,b=[];for(let d;d=a.exec(c);)b.push(parseInt(d[2]));if(b.length){let a=.01*Math.min(...b);return{widths:e.filter(b=>b>=d[0]*a),kind:"w"}}return{widths:e,kind:"w"}}return"number"!=typeof b?{widths:d,kind:"w"}:{widths:[...new Set([b,2*b].map(a=>e.find(b=>b>=a)||e[e.length-1]))],kind:"x"}}(b,e,g),k=i.length-1;return{sizes:g||"w"!==j?g:"100vw",srcSet:i.map((a,d)=>h({config:b,src:c,quality:f,width:a})+" "+("w"===j?a:d+1)+j).join(", "),src:h({config:b,src:c,quality:f,width:i[k]})}}({config:j,src:m,unoptimized:o,width:S,quality:V,sizes:n,loader:P});return{props:{...J,loading:U?"lazy":q,fetchPriority:C,width:S,height:T,decoding:D,className:r,style:{...W,...Z},sizes:$.sizes,srcSet:$.srcSet,src:x||$.src},meta:{unoptimized:o,priority:p,placeholder:A,fill:v}}}},69412:(a,b,c)=>{"use strict";c.d(b,{LanguageToggle:()=>e,LanguageToggleText:()=>f});var d=c(66352);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call LanguageToggle() from the server but LanguageToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/language-toggle.js","LanguageToggle"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call LanguageToggleText() from the server but LanguageToggleText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/language-toggle.js","LanguageToggleText")},69769:(a,b,c)=>{"use strict";c.d(b,{ThemeToggle:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js","ThemeToggle")},69938:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=c(10097).unstable_rethrow;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},70642:(a,b,c)=>{Promise.resolve().then(c.bind(c,3674)),Promise.resolve().then(c.bind(c,71896)),Promise.resolve().then(c.bind(c,69412)),Promise.resolve().then(c.bind(c,6482)),Promise.resolve().then(c.bind(c,2470)),Promise.resolve().then(c.bind(c,80085)),Promise.resolve().then(c.bind(c,69769)),Promise.resolve().then(c.bind(c,29038)),Promise.resolve().then(c.bind(c,46964)),Promise.resolve().then(c.bind(c,98404)),Promise.resolve().then(c.bind(c,1048)),Promise.resolve().then(c.bind(c,86987)),Promise.resolve().then(c.bind(c,99023)),Promise.resolve().then(c.bind(c,79104)),Promise.resolve().then(c.bind(c,3980))},71896:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/link.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/link.js","default")},73187:(a,b,c)=>{"use strict";c.d(b,{AT:()=>i,Bx:()=>j,EY:()=>k,ff:()=>l});var d=c(38828),e=c(61365),f=c(8512);let g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},h=(a,b)=>{let c=(0,e.forwardRef)(({className:a,size:c=24,color:h="currentColor",children:i,...j},k)=>(0,d.jsxs)("svg",{ref:k,...g,width:c,height:c,stroke:h,className:(0,f.QP)("lucide",a),...j,children:[b.map(([a,b])=>(0,e.createElement)(a,b)),i]}));return c.displayName=a,c};h("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);let i=h("languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]),j=h("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);h("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]),h("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),h("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),h("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),h("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),h("airplay",[["path",{d:"M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1",key:"ns4c3b"}],["path",{d:"m12 15 5 6H7Z",key:"14qnn2"}]]),h("menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),h("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),h("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),h("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),h("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),h("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),h("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),h("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),h("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),h("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),h("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);let k=h("text",[["path",{d:"M15 18H3",key:"olowqp"}],["path",{d:"M17 6H3",key:"16j9eg"}],["path",{d:"M21 12H3",key:"2avoz0"}]]);h("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]),h("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]),h("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]),h("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),h("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);let l=h("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);h("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),h("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),h("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),h("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),h("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},75062:(a,b,c)=>{"use strict";c.d(b,{CollapsibleControl:()=>o,LayoutBody:()=>m,Navbar:()=>l,SidebarTrigger:()=>n});var d=c(13486),e=c(19037),f=c(91518),g=c(93507),h=c(21944),i=c(10640),j=c(46184),k=c(1652);function l(a){let{isTransparent:b}=(0,i.hI)();return(0,d.jsx)("header",{id:"nd-subnav",...a,className:(0,f.QP)("fixed top-(--fd-banner-height) inset-x-0 z-30 flex items-center ps-4 pe-2.5 border-b transition-colors backdrop-blur-sm",!b&&"bg-fd-background/80",a.className),children:a.children})}function m(a){let{collapsed:b}=(0,h.c)();return(0,d.jsx)("main",{id:"nd-docs-layout",...a,className:(0,f.QP)("flex flex-1 flex-col transition-[margin]",a.className),style:{...a.style,marginInlineStart:b?"max(0px, min(calc(100vw - var(--fd-page-width)), var(--fd-sidebar-width)))":"var(--fd-sidebar-width)"},children:a.children})}function n({children:a,...b}){let{setOpen:c}=(0,h.c)();return(0,d.jsx)("button",{...b,"aria-label":"Open Sidebar",onClick:()=>c(a=>!a),children:a})}function o(){let{collapsed:a}=(0,h.c)();return(0,d.jsxs)("div",{className:(0,f.QP)("fixed flex shadow-lg transition-opacity rounded-xl p-0.5 border bg-fd-muted text-fd-muted-foreground z-10 max-md:hidden xl:start-4 max-xl:end-4",!a&&"pointer-events-none opacity-0"),style:{top:"calc(var(--fd-banner-height) + var(--fd-tocnav-height) + var(--spacing) * 4)"},children:[(0,d.jsx)(j.SidebarCollapseTrigger,{className:(0,f.QP)((0,g.r)({color:"ghost",size:"icon-sm",className:"rounded-lg"})),children:(0,d.jsx)(e.Bx,{})}),(0,d.jsx)(k.SearchToggle,{className:"rounded-lg",hideIfDisabled:!0})]})}},75636:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"Image",{enumerable:!0,get:function(){return u}});let d=c(50686),e=c(15881),f=c(13486),g=e._(c(60159)),h=d._(c(22358)),i=d._(c(27923)),j=c(69356),k=c(51820),l=c(50298);c(12405);let m=c(10545),n=d._(c(92122)),o=c(76181),p={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function q(a,b,c,d,e,f,g){let h=null==a?void 0:a.src;a&&a["data-loaded-src"]!==h&&(a["data-loaded-src"]=h,("decode"in a?a.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(a.parentElement&&a.isConnected){if("empty"!==b&&e(!0),null==c?void 0:c.current){let b=new Event("load");Object.defineProperty(b,"target",{writable:!1,value:a});let d=!1,e=!1;c.current({...b,nativeEvent:b,currentTarget:a,target:a,isDefaultPrevented:()=>d,isPropagationStopped:()=>e,persist:()=>{},preventDefault:()=>{d=!0,b.preventDefault()},stopPropagation:()=>{e=!0,b.stopPropagation()}})}(null==d?void 0:d.current)&&d.current(a)}}))}function r(a){return g.use?{fetchPriority:a}:{fetchpriority:a}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let s=(0,g.forwardRef)((a,b)=>{let{src:c,srcSet:d,sizes:e,height:h,width:i,decoding:j,className:k,style:l,fetchPriority:m,placeholder:n,loading:p,unoptimized:s,fill:t,onLoadRef:u,onLoadingCompleteRef:v,setBlurComplete:w,setShowAltText:x,sizesInput:y,onLoad:z,onError:A,...B}=a,C=(0,g.useCallback)(a=>{a&&(A&&(a.src=a.src),a.complete&&q(a,n,u,v,w,s,y))},[c,n,u,v,w,A,s,y]),D=(0,o.useMergedRef)(b,C);return(0,f.jsx)("img",{...B,...r(m),loading:p,width:i,height:h,decoding:j,"data-nimg":t?"fill":"1",className:k,style:l,sizes:e,srcSet:d,src:c,ref:D,onLoad:a=>{q(a.currentTarget,n,u,v,w,s,y)},onError:a=>{x(!0),"empty"!==n&&w(!0),A&&A(a)}})});function t(a){let{isAppRouter:b,imgAttributes:c}=a,d={as:"image",imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:c.crossOrigin,referrerPolicy:c.referrerPolicy,...r(c.fetchPriority)};return b&&h.default.preload?(h.default.preload(c.src,d),null):(0,f.jsx)(i.default,{children:(0,f.jsx)("link",{rel:"preload",href:c.srcSet?void 0:c.src,...d},"__nimg-"+c.src+c.srcSet+c.sizes)})}let u=(0,g.forwardRef)((a,b)=>{let c=(0,g.useContext)(m.RouterContext),d=(0,g.useContext)(l.ImageConfigContext),e=(0,g.useMemo)(()=>{var a;let b=p||d||k.imageConfigDefault,c=[...b.deviceSizes,...b.imageSizes].sort((a,b)=>a-b),e=b.deviceSizes.sort((a,b)=>a-b),f=null==(a=b.qualities)?void 0:a.sort((a,b)=>a-b);return{...b,allSizes:c,deviceSizes:e,qualities:f}},[d]),{onLoad:h,onLoadingComplete:i}=a,o=(0,g.useRef)(h);(0,g.useEffect)(()=>{o.current=h},[h]);let q=(0,g.useRef)(i);(0,g.useEffect)(()=>{q.current=i},[i]);let[r,u]=(0,g.useState)(!1),[v,w]=(0,g.useState)(!1),{props:x,meta:y}=(0,j.getImgProps)(a,{defaultLoader:n.default,imgConf:e,blurComplete:r,showAltText:v});return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(s,{...x,unoptimized:y.unoptimized,placeholder:y.placeholder,fill:y.fill,onLoadRef:o,onLoadingCompleteRef:q,setBlurComplete:u,setShowAltText:w,sizesInput:a.sizes,ref:b}),y.priority?(0,f.jsx)(t,{isAppRouter:!c,imgAttributes:x}):null]})});("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},76322:(a,b,c)=>{"use strict";c.d(b,{BaseLinkItem:()=>i});var d=c(13486),e=c(55703),f=c(86663),g=c(60159),h=c(86232);let i=(0,g.forwardRef)(({item:a,...b},c)=>{let g=(0,f.a8)(),i=a.active??"url",j="none"!==i&&(0,h.$)(a.url,g,"nested-url"===i);return(0,d.jsx)(e.default,{ref:c,href:a.url,external:a.external,...b,"data-active":j,children:b.children})});i.displayName="BaseLinkItem"},76353:(a,b,c)=>{"use strict";c.d(b,{F:()=>g});var d=c(4627);let e=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,f=d.$,g=(a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return f(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:g,defaultVariants:h}=b,i=Object.keys(g).map(a=>{let b=null==c?void 0:c[a],d=null==h?void 0:h[a];if(null===b)return null;let f=e(b)||e(d);return g[a][f]}),j=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return f(a,i,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...h,...j}[b]):({...h,...j})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)}},77670:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unauthorized",{enumerable:!0,get:function(){return d}}),c(22859).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},77795:(a,b,c)=>{"use strict";c.d(b,{TOCItems:()=>f,TOCScrollArea:()=>e});var d=c(66352);(0,d.registerClientReference)(function(){throw Error("Attempted to call useTOCItems() from the server but useTOCItems is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/toc.js","useTOCItems"),(0,d.registerClientReference)(function(){throw Error("Attempted to call TOCProvider() from the server but TOCProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/toc.js","TOCProvider");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call TOCScrollArea() from the server but TOCScrollArea is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/toc.js","TOCScrollArea"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call TOCItems() from the server but TOCItems is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/toc.js","TOCItems")},78041:(a,b,c)=>{"use strict";c.d(b,{TOCItems:()=>o,TOCScrollArea:()=>n,W:()=>m,a:()=>l});var d=c(13486),e=c(48878),f=c(60159),g=c(91518),h=c(9650),i=c(35476),j=c(85624);let k=(0,f.createContext)([]);function l(){return(0,f.useContext)(k)}function m({toc:a,children:b,...c}){return(0,d.jsx)(k,{value:a,children:(0,d.jsx)(e.NQ,{toc:a,...c,children:b})})}function n({ref:a,className:b,...c}){let h=(0,f.useRef)(null);return(0,d.jsx)("div",{ref:(0,j.P)(h,a),className:(0,g.QP)("relative min-h-0 text-sm ms-px overflow-auto [scrollbar-width:none] [mask-image:linear-gradient(to_bottom,transparent,white_16px,white_calc(100%-16px),transparent)] py-3",b),...c,children:(0,d.jsx)(e.N2,{containerRef:h,children:c.children})})}function o({ref:a,className:b,...c}){let e=(0,f.useRef)(null),k=l(),{text:m}=(0,h.useI18n)();return 0===k.length?(0,d.jsx)("div",{className:"rounded-lg border bg-fd-card p-3 text-xs text-fd-muted-foreground",children:m.tocNoHeadings}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(i.j,{containerRef:e,className:"absolute top-(--fd-top) h-(--fd-height) w-px bg-fd-primary transition-all"}),(0,d.jsx)("div",{ref:(0,j.P)(a,e),className:(0,g.QP)("flex flex-col border-s border-fd-foreground/10",b),...c,children:k.map(a=>(0,d.jsx)(p,{item:a},a.url))})]})}function p({item:a}){return(0,d.jsx)(e.Cz,{href:a.url,className:(0,g.QP)("prose py-1.5 text-sm text-fd-muted-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary",a.depth<=2&&"ps-3",3===a.depth&&"ps-6",a.depth>=4&&"ps-8"),children:a.title})}},79104:(a,b,c)=>{"use strict";c.d(b,{QueryProvider:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/query-provider.tsx","QueryProvider")},80085:(a,b,c)=>{"use strict";c.r(b),c.d(b,{Sidebar:()=>e,SidebarCollapseTrigger:()=>q,SidebarContent:()=>f,SidebarContentMobile:()=>g,SidebarFolder:()=>m,SidebarFolderContent:()=>p,SidebarFolderLink:()=>o,SidebarFolderTrigger:()=>n,SidebarFooter:()=>i,SidebarHeader:()=>h,SidebarItem:()=>l,SidebarPageTree:()=>r,SidebarSeparator:()=>k,SidebarViewport:()=>j});var d=c(66352);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","Sidebar"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarContent"),g=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarContentMobile() from the server but SidebarContentMobile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarContentMobile"),h=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarHeader"),i=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarFooter"),j=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarViewport() from the server but SidebarViewport is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarViewport"),k=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarSeparator"),l=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarItem() from the server but SidebarItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarItem"),m=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarFolder() from the server but SidebarFolder is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarFolder"),n=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarFolderTrigger() from the server but SidebarFolderTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarFolderTrigger"),o=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarFolderLink() from the server but SidebarFolderLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarFolderLink"),p=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarFolderContent() from the server but SidebarFolderContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarFolderContent"),q=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarCollapseTrigger() from the server but SidebarCollapseTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarCollapseTrigger"),r=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarPageTree() from the server but SidebarPageTree is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js","SidebarPageTree")},83374:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{isRequestAPICallableInsideAfter:function(){return i},throwForSearchParamsAccessInUseCache:function(){return h},throwWithStaticGenerationBailoutError:function(){return f},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return g}});let d=c(10960),e=c(3295);function f(a,b){throw Object.defineProperty(new d.StaticGenBailoutError(`Route ${a} couldn't be rendered statically because it used ${b}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function g(a,b){throw Object.defineProperty(new d.StaticGenBailoutError(`Route ${a} with \`dynamic = "error"\` couldn't be rendered statically because it used ${b}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function h(a,b){let c=Object.defineProperty(Error(`Route ${a.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw Error.captureStackTrace(c,b),a.invalidDynamicUsageError??=c,c}function i(){let a=e.afterTaskAsyncStorage.getStore();return(null==a?void 0:a.rootTaskSpawnPhase)==="action"}},85624:(a,b,c)=>{"use strict";function d(...a){return b=>{a.forEach(a=>{"function"==typeof a?a(b):a&&(a.current=b)})}}c.d(b,{P:()=>d})},85936:(a,b,c)=>{"use strict";a.exports=c(69358).vendored.contexts.AmpContext},86232:(a,b,c)=>{"use strict";function d(a,b,c=!0){return a.endsWith("/")&&(a=a.slice(0,-1)),b.endsWith("/")&&(b=b.slice(0,-1)),a===b||c&&b.startsWith(`${a}/`)}c.d(b,{$:()=>d})},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86663:(a,b,c)=>{"use strict";c.d(b,{a8:()=>d.a8,q6:()=>d.q6,rd:()=>d.rd});var d=c(28186);c(68926)},86987:(a,b,c)=>{"use strict";c.d(b,{RootProvider:()=>e});var d=c(66352);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call RootProvider() from the server but RootProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","RootProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useI18n() from the server but useI18n is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","useI18n"),(0,d.registerClientReference)(function(){throw Error("Attempted to call I18nLabel() from the server but I18nLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","I18nLabel"),(0,d.registerClientReference)(function(){throw Error("Attempted to call SearchProvider() from the server but SearchProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","SearchProvider"),(0,d.registerClientReference)(function(){throw Error("Attempted to call SearchOnly() from the server but SearchOnly is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","SearchOnly"),(0,d.registerClientReference)(function(){throw Error("Attempted to call useSearchContext() from the server but useSearchContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","useSearchContext"),(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","SidebarProvider"),(0,d.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","useSidebar"),(0,d.registerClientReference)(function(){throw Error("Attempted to call useTreePath() from the server but useTreePath is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","useTreePath"),(0,d.registerClientReference)(function(){throw Error("Attempted to call useTreeContext() from the server but useTreeContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","useTreeContext"),(0,d.registerClientReference)(function(){throw Error("Attempted to call TreeContextProvider() from the server but TreeContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","TreeContextProvider"),(0,d.registerClientReference)(function(){throw Error("Attempted to call useNav() from the server but useNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","useNav"),(0,d.registerClientReference)(function(){throw Error("Attempted to call NavProvider() from the server but NavProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","NavProvider"),(0,d.registerClientReference)(function(){throw Error("Attempted to call StylesProvider() from the server but StylesProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","StylesProvider"),(0,d.registerClientReference)(function(){throw Error("Attempted to call usePageStyles() from the server but usePageStyles is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js","usePageStyles")},91518:(a,b,c)=>{"use strict";c.d(b,{QP:()=>aa});let d=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],e=b.nextPart.get(c),f=e?d(a.slice(1),e):void 0;if(f)return f;if(0===b.validators.length)return;let g=a.join("-");return b.validators.find(({validator:a})=>a(g))?.classGroupId},e=/^\[(.+)\]$/,f=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:g(b,a)).classGroupId=c;return}if("function"==typeof a)return h(a)?void f(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{f(e,g(b,a),c,d)})})},g=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},h=a=>a.isThemeGetter,i=/\s+/;function j(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=k(a))&&(d&&(d+=" "),d+=b);return d}let k=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=k(a[d]))&&(c&&(c+=" "),c+=b);return c},l=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},m=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,n=/^\((?:(\w[\w-]*):)?(.+)\)$/i,o=/^\d+\/\d+$/,p=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,q=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,r=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,s=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,t=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,u=a=>o.test(a),v=a=>!!a&&!Number.isNaN(Number(a)),w=a=>!!a&&Number.isInteger(Number(a)),x=a=>a.endsWith("%")&&v(a.slice(0,-1)),y=a=>p.test(a),z=()=>!0,A=a=>q.test(a)&&!r.test(a),B=()=>!1,C=a=>s.test(a),D=a=>t.test(a),E=a=>!G(a)&&!M(a),F=a=>T(a,X,B),G=a=>m.test(a),H=a=>T(a,Y,A),I=a=>T(a,Z,v),J=a=>T(a,V,B),K=a=>T(a,W,D),L=a=>T(a,_,C),M=a=>n.test(a),N=a=>U(a,Y),O=a=>U(a,$),P=a=>U(a,V),Q=a=>U(a,X),R=a=>U(a,W),S=a=>U(a,_,!0),T=(a,b,c)=>{let d=m.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},U=(a,b,c=!1)=>{let d=n.exec(a);return!!d&&(d[1]?b(d[1]):c)},V=a=>"position"===a||"percentage"===a,W=a=>"image"===a||"url"===a,X=a=>"length"===a||"size"===a||"bg-size"===a,Y=a=>"length"===a,Z=a=>"number"===a,$=a=>"family-name"===a,_=a=>"shadow"===a;Symbol.toStringTag;let aa=function(a,...b){let c,g,h,k=function(i){let j;return g=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((j=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(j),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(j),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)f(c[a],d,a,b);return d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:g}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),d(c,b)||(a=>{if(e.test(a)){let b=e.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let d=c[a]||[];return b&&g[a]?[...d,...g[a]]:d}}})(j)}).cache.get,h=c.cache.set,k=l,l(i)};function l(a){let b=g(a);if(b)return b;let d=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(i),j="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:i,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(i){j=b+(j.length>0?" "+j:j);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){j=b+(j.length>0?" "+j:j);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}j=b+(j.length>0?" "+j:j)}return j})(a,c);return h(a,d),d}return function(){return k(j.apply(null,arguments))}}(()=>{let a=l("color"),b=l("font"),c=l("text"),d=l("font-weight"),e=l("tracking"),f=l("leading"),g=l("breakpoint"),h=l("container"),i=l("spacing"),j=l("radius"),k=l("shadow"),m=l("inset-shadow"),n=l("text-shadow"),o=l("drop-shadow"),p=l("blur"),q=l("perspective"),r=l("aspect"),s=l("ease"),t=l("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...B(),M,G],D=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto","contain","none"],U=()=>[M,G,i],V=()=>[u,"full","auto",...U()],W=()=>[w,"none","subgrid",M,G],X=()=>["auto",{span:["full",w,M,G]},w,M,G],Y=()=>[w,"auto",M,G],Z=()=>["auto","min","max","fr",M,G],$=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],aa=()=>["auto",...U()],ab=()=>[u,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...U()],ac=()=>[a,M,G],ad=()=>[...B(),P,J,{position:[M,G]}],ae=()=>["no-repeat",{repeat:["","x","y","space","round"]}],af=()=>["auto","cover","contain",Q,F,{size:[M,G]}],ag=()=>[x,N,H],ah=()=>["","none","full",j,M,G],ai=()=>["",v,N,H],aj=()=>["solid","dashed","dotted","double"],ak=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],al=()=>[v,x,P,J],am=()=>["","none",p,M,G],an=()=>["none",v,M,G],ao=()=>["none",v,M,G],ap=()=>[v,M,G],aq=()=>[u,"full",...U()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[y],breakpoint:[y],color:[z],container:[y],"drop-shadow":[y],ease:["in","out","in-out"],font:[E],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[y],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[y],shadow:[y],spacing:["px",v],text:[y],"text-shadow":[y],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",u,G,M,r]}],container:["container"],columns:[{columns:[v,G,M,h]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:D()}],"overflow-x":[{"overflow-x":D()}],"overflow-y":[{"overflow-y":D()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:V()}],"inset-x":[{"inset-x":V()}],"inset-y":[{"inset-y":V()}],start:[{start:V()}],end:[{end:V()}],top:[{top:V()}],right:[{right:V()}],bottom:[{bottom:V()}],left:[{left:V()}],visibility:["visible","invisible","collapse"],z:[{z:[w,"auto",M,G]}],basis:[{basis:[u,"full","auto",h,...U()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[v,u,"auto","initial","none",G]}],grow:[{grow:["",v,M,G]}],shrink:[{shrink:["",v,M,G]}],order:[{order:[w,"first","last","none",M,G]}],"grid-cols":[{"grid-cols":W()}],"col-start-end":[{col:X()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":W()}],"row-start-end":[{row:X()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:U()}],"gap-x":[{"gap-x":U()}],"gap-y":[{"gap-y":U()}],"justify-content":[{justify:[...$(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...$()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":$()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:U()}],px:[{px:U()}],py:[{py:U()}],ps:[{ps:U()}],pe:[{pe:U()}],pt:[{pt:U()}],pr:[{pr:U()}],pb:[{pb:U()}],pl:[{pl:U()}],m:[{m:aa()}],mx:[{mx:aa()}],my:[{my:aa()}],ms:[{ms:aa()}],me:[{me:aa()}],mt:[{mt:aa()}],mr:[{mr:aa()}],mb:[{mb:aa()}],ml:[{ml:aa()}],"space-x":[{"space-x":U()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":U()}],"space-y-reverse":["space-y-reverse"],size:[{size:ab()}],w:[{w:[h,"screen",...ab()]}],"min-w":[{"min-w":[h,"screen","none",...ab()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...ab()]}],h:[{h:["screen","lh",...ab()]}],"min-h":[{"min-h":["screen","lh","none",...ab()]}],"max-h":[{"max-h":["screen","lh",...ab()]}],"font-size":[{text:["base",c,N,H]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,M,I]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",x,G]}],"font-family":[{font:[O,G,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,M,G]}],"line-clamp":[{"line-clamp":[v,"none",M,I]}],leading:[{leading:[f,...U()]}],"list-image":[{"list-image":["none",M,G]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",M,G]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ac()}],"text-color":[{text:ac()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...aj(),"wavy"]}],"text-decoration-thickness":[{decoration:[v,"from-font","auto",M,H]}],"text-decoration-color":[{decoration:ac()}],"underline-offset":[{"underline-offset":[v,"auto",M,G]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:U()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",M,G]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",M,G]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ad()}],"bg-repeat":[{bg:ae()}],"bg-size":[{bg:af()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},w,M,G],radial:["",M,G],conic:[w,M,G]},R,K]}],"bg-color":[{bg:ac()}],"gradient-from-pos":[{from:ag()}],"gradient-via-pos":[{via:ag()}],"gradient-to-pos":[{to:ag()}],"gradient-from":[{from:ac()}],"gradient-via":[{via:ac()}],"gradient-to":[{to:ac()}],rounded:[{rounded:ah()}],"rounded-s":[{"rounded-s":ah()}],"rounded-e":[{"rounded-e":ah()}],"rounded-t":[{"rounded-t":ah()}],"rounded-r":[{"rounded-r":ah()}],"rounded-b":[{"rounded-b":ah()}],"rounded-l":[{"rounded-l":ah()}],"rounded-ss":[{"rounded-ss":ah()}],"rounded-se":[{"rounded-se":ah()}],"rounded-ee":[{"rounded-ee":ah()}],"rounded-es":[{"rounded-es":ah()}],"rounded-tl":[{"rounded-tl":ah()}],"rounded-tr":[{"rounded-tr":ah()}],"rounded-br":[{"rounded-br":ah()}],"rounded-bl":[{"rounded-bl":ah()}],"border-w":[{border:ai()}],"border-w-x":[{"border-x":ai()}],"border-w-y":[{"border-y":ai()}],"border-w-s":[{"border-s":ai()}],"border-w-e":[{"border-e":ai()}],"border-w-t":[{"border-t":ai()}],"border-w-r":[{"border-r":ai()}],"border-w-b":[{"border-b":ai()}],"border-w-l":[{"border-l":ai()}],"divide-x":[{"divide-x":ai()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ai()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...aj(),"hidden","none"]}],"divide-style":[{divide:[...aj(),"hidden","none"]}],"border-color":[{border:ac()}],"border-color-x":[{"border-x":ac()}],"border-color-y":[{"border-y":ac()}],"border-color-s":[{"border-s":ac()}],"border-color-e":[{"border-e":ac()}],"border-color-t":[{"border-t":ac()}],"border-color-r":[{"border-r":ac()}],"border-color-b":[{"border-b":ac()}],"border-color-l":[{"border-l":ac()}],"divide-color":[{divide:ac()}],"outline-style":[{outline:[...aj(),"none","hidden"]}],"outline-offset":[{"outline-offset":[v,M,G]}],"outline-w":[{outline:["",v,N,H]}],"outline-color":[{outline:ac()}],shadow:[{shadow:["","none",k,S,L]}],"shadow-color":[{shadow:ac()}],"inset-shadow":[{"inset-shadow":["none",m,S,L]}],"inset-shadow-color":[{"inset-shadow":ac()}],"ring-w":[{ring:ai()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ac()}],"ring-offset-w":[{"ring-offset":[v,H]}],"ring-offset-color":[{"ring-offset":ac()}],"inset-ring-w":[{"inset-ring":ai()}],"inset-ring-color":[{"inset-ring":ac()}],"text-shadow":[{"text-shadow":["none",n,S,L]}],"text-shadow-color":[{"text-shadow":ac()}],opacity:[{opacity:[v,M,G]}],"mix-blend":[{"mix-blend":[...ak(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ak()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[v]}],"mask-image-linear-from-pos":[{"mask-linear-from":al()}],"mask-image-linear-to-pos":[{"mask-linear-to":al()}],"mask-image-linear-from-color":[{"mask-linear-from":ac()}],"mask-image-linear-to-color":[{"mask-linear-to":ac()}],"mask-image-t-from-pos":[{"mask-t-from":al()}],"mask-image-t-to-pos":[{"mask-t-to":al()}],"mask-image-t-from-color":[{"mask-t-from":ac()}],"mask-image-t-to-color":[{"mask-t-to":ac()}],"mask-image-r-from-pos":[{"mask-r-from":al()}],"mask-image-r-to-pos":[{"mask-r-to":al()}],"mask-image-r-from-color":[{"mask-r-from":ac()}],"mask-image-r-to-color":[{"mask-r-to":ac()}],"mask-image-b-from-pos":[{"mask-b-from":al()}],"mask-image-b-to-pos":[{"mask-b-to":al()}],"mask-image-b-from-color":[{"mask-b-from":ac()}],"mask-image-b-to-color":[{"mask-b-to":ac()}],"mask-image-l-from-pos":[{"mask-l-from":al()}],"mask-image-l-to-pos":[{"mask-l-to":al()}],"mask-image-l-from-color":[{"mask-l-from":ac()}],"mask-image-l-to-color":[{"mask-l-to":ac()}],"mask-image-x-from-pos":[{"mask-x-from":al()}],"mask-image-x-to-pos":[{"mask-x-to":al()}],"mask-image-x-from-color":[{"mask-x-from":ac()}],"mask-image-x-to-color":[{"mask-x-to":ac()}],"mask-image-y-from-pos":[{"mask-y-from":al()}],"mask-image-y-to-pos":[{"mask-y-to":al()}],"mask-image-y-from-color":[{"mask-y-from":ac()}],"mask-image-y-to-color":[{"mask-y-to":ac()}],"mask-image-radial":[{"mask-radial":[M,G]}],"mask-image-radial-from-pos":[{"mask-radial-from":al()}],"mask-image-radial-to-pos":[{"mask-radial-to":al()}],"mask-image-radial-from-color":[{"mask-radial-from":ac()}],"mask-image-radial-to-color":[{"mask-radial-to":ac()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":B()}],"mask-image-conic-pos":[{"mask-conic":[v]}],"mask-image-conic-from-pos":[{"mask-conic-from":al()}],"mask-image-conic-to-pos":[{"mask-conic-to":al()}],"mask-image-conic-from-color":[{"mask-conic-from":ac()}],"mask-image-conic-to-color":[{"mask-conic-to":ac()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ad()}],"mask-repeat":[{mask:ae()}],"mask-size":[{mask:af()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",M,G]}],filter:[{filter:["","none",M,G]}],blur:[{blur:am()}],brightness:[{brightness:[v,M,G]}],contrast:[{contrast:[v,M,G]}],"drop-shadow":[{"drop-shadow":["","none",o,S,L]}],"drop-shadow-color":[{"drop-shadow":ac()}],grayscale:[{grayscale:["",v,M,G]}],"hue-rotate":[{"hue-rotate":[v,M,G]}],invert:[{invert:["",v,M,G]}],saturate:[{saturate:[v,M,G]}],sepia:[{sepia:["",v,M,G]}],"backdrop-filter":[{"backdrop-filter":["","none",M,G]}],"backdrop-blur":[{"backdrop-blur":am()}],"backdrop-brightness":[{"backdrop-brightness":[v,M,G]}],"backdrop-contrast":[{"backdrop-contrast":[v,M,G]}],"backdrop-grayscale":[{"backdrop-grayscale":["",v,M,G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[v,M,G]}],"backdrop-invert":[{"backdrop-invert":["",v,M,G]}],"backdrop-opacity":[{"backdrop-opacity":[v,M,G]}],"backdrop-saturate":[{"backdrop-saturate":[v,M,G]}],"backdrop-sepia":[{"backdrop-sepia":["",v,M,G]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":U()}],"border-spacing-x":[{"border-spacing-x":U()}],"border-spacing-y":[{"border-spacing-y":U()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",M,G]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[v,"initial",M,G]}],ease:[{ease:["linear","initial",s,M,G]}],delay:[{delay:[v,M,G]}],animate:[{animate:["none",t,M,G]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[q,M,G]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:an()}],"rotate-x":[{"rotate-x":an()}],"rotate-y":[{"rotate-y":an()}],"rotate-z":[{"rotate-z":an()}],scale:[{scale:ao()}],"scale-x":[{"scale-x":ao()}],"scale-y":[{"scale-y":ao()}],"scale-z":[{"scale-z":ao()}],"scale-3d":["scale-3d"],skew:[{skew:ap()}],"skew-x":[{"skew-x":ap()}],"skew-y":[{"skew-y":ap()}],transform:[{transform:[M,G,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:aq()}],"translate-x":[{"translate-x":aq()}],"translate-y":[{"translate-y":aq()}],"translate-z":[{"translate-z":aq()}],"translate-none":["translate-none"],accent:[{accent:ac()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ac()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",M,G]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":U()}],"scroll-mx":[{"scroll-mx":U()}],"scroll-my":[{"scroll-my":U()}],"scroll-ms":[{"scroll-ms":U()}],"scroll-me":[{"scroll-me":U()}],"scroll-mt":[{"scroll-mt":U()}],"scroll-mr":[{"scroll-mr":U()}],"scroll-mb":[{"scroll-mb":U()}],"scroll-ml":[{"scroll-ml":U()}],"scroll-p":[{"scroll-p":U()}],"scroll-px":[{"scroll-px":U()}],"scroll-py":[{"scroll-py":U()}],"scroll-ps":[{"scroll-ps":U()}],"scroll-pe":[{"scroll-pe":U()}],"scroll-pt":[{"scroll-pt":U()}],"scroll-pr":[{"scroll-pr":U()}],"scroll-pb":[{"scroll-pb":U()}],"scroll-pl":[{"scroll-pl":U()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",M,G]}],fill:[{fill:["none",...ac()]}],"stroke-w":[{stroke:[v,N,H,I]}],stroke:[{stroke:["none",...ac()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},92122:(a,b)=>{"use strict";function c(a){var b;let{config:c,src:d,width:e,quality:f}=a,g=f||(null==(b=c.qualities)?void 0:b.reduce((a,b)=>Math.abs(b-75)<Math.abs(a-75)?b:a))||75;return c.path+"?url="+encodeURIComponent(d)+"&w="+e+"&q="+g+(d.startsWith("/_next/static/media/"),"")}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return d}}),c.__next_img_default=!0;let d=c},93507:(a,b,c)=>{"use strict";c.d(b,{r:()=>d});let d=(0,c(76353).F)("inline-flex items-center justify-center rounded-md p-2 text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none",{variants:{color:{primary:"bg-fd-primary text-fd-primary-foreground hover:bg-fd-primary/80",outline:"border hover:bg-fd-accent hover:text-fd-accent-foreground",ghost:"hover:bg-fd-accent hover:text-fd-accent-foreground",secondary:"border bg-fd-secondary text-fd-secondary-foreground hover:bg-fd-accent hover:text-fd-accent-foreground"},size:{sm:"gap-1 px-2 py-1.5 text-xs",icon:"p-1.5 [&_svg]:size-5","icon-sm":"p-1.5 [&_svg]:size-4.5","icon-xs":"p-1 [&_svg]:size-4"}}})},93687:(a,b,c)=>{"use strict";c.d(b,{Ke:()=>j,Nt:()=>h,R6:()=>i});var d=c(13486),e=c(7377),f=c(60159),g=c(91518);let h=e.Root,i=e.CollapsibleTrigger,j=(0,f.forwardRef)(({children:a,...b},c)=>{let[h,i]=(0,f.useState)(!1);return(0,f.useEffect)(()=>{i(!0)},[]),(0,d.jsx)(e.CollapsibleContent,{ref:c,...b,className:(0,g.QP)("overflow-hidden",h&&"data-[state=closed]:animate-fd-collapsible-up data-[state=open]:animate-fd-collapsible-down",b.className),children:a})});j.displayName=e.CollapsibleContent.displayName},94638:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>B.default,__next_app__:()=>H,handler:()=>J,pages:()=>G,routeModule:()=>I,tree:()=>F});var d=c(24332),e=c(48819),f=c(93949),g=c(98730),h=c(88996),i=c(16318),j=c(3093),k=c(36748),l=c(98190),m=c(53904),n=c(47735),o=c(20611),p=c(22512),q=c(261),r=c(13863),s=c(8748),t=c(26713),u=c(65262),v=c(97779),w=c(5303),x=c(66704),y=c(67656),z=c(3072),A=c(86439),B=c(866),C=c(97540),D=c(49005),E={};for(let a in C)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(E[a]=()=>C[a]);c.d(b,E);let F={children:["",{children:["(docs)",{children:["docs",{children:["[[...slug]]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,40356)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/docs/[[...slug]]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,14294)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,50134,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{"global-error":[()=>Promise.resolve().then(c.bind(c,866)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,866)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,50134,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,G=["/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/docs/[[...slug]]/page.tsx"],H={require:c,loadChunk:()=>Promise.resolve()},I=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(docs)/docs/[[...slug]]/page",pathname:"/docs/[[...slug]]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:F},distDir:".next",projectDir:""});async function J(a,b,c){var d;let E="/(docs)/docs/[[...slug]]/page";"/index"===E&&(E="/");let K="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await I.prepare(a,b,{srcPage:E,multiZoneDraftMode:K});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(E),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===I.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&I.isDev&&(az=_);let aA={...C,tree:F,pages:G,GlobalError:B.default,handler:J,routeModule:I,__next_app__:H};W&&X&&(0,n.setReferenceManifestsSingleton)({page:E,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return I.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:I,page:E,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:I.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:K,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>I.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:I.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!I.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===I.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await I.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await I.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),I.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!I.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&D.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await I.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},95284:(a,b,c)=>{"use strict";c.d(b,{PageBreadcrumb:()=>k,PageFooter:()=>j,PageLastUpdate:()=>i,PageRoot:()=>h,PageTOC:()=>l,PageTOCPopover:()=>g,PageTOCPopoverContent:()=>f,PageTOCPopoverTrigger:()=>e});var d=c(66352);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call PageTOCPopoverTrigger() from the server but PageTOCPopoverTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","PageTOCPopoverTrigger"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call PageTOCPopoverContent() from the server but PageTOCPopoverContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","PageTOCPopoverContent"),g=(0,d.registerClientReference)(function(){throw Error("Attempted to call PageTOCPopover() from the server but PageTOCPopover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","PageTOCPopover"),h=(0,d.registerClientReference)(function(){throw Error("Attempted to call PageRoot() from the server but PageRoot is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","PageRoot"),i=(0,d.registerClientReference)(function(){throw Error("Attempted to call PageLastUpdate() from the server but PageLastUpdate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","PageLastUpdate"),j=(0,d.registerClientReference)(function(){throw Error("Attempted to call PageFooter() from the server but PageFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","PageFooter"),k=(0,d.registerClientReference)(function(){throw Error("Attempted to call PageBreadcrumb() from the server but PageBreadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","PageBreadcrumb"),l=(0,d.registerClientReference)(function(){throw Error("Attempted to call PageTOC() from the server but PageTOC is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","PageTOC")},95670:(a,b,c)=>{"use strict";c.d(b,{AM:()=>h,Wv:()=>i,hl:()=>j});var d=c(13486),e=c(22584),f=c(60159),g=c(91518);let h=e.Root,i=e.Trigger,j=f.forwardRef(({className:a,align:b="center",sideOffset:c=4,...f},h)=>(0,d.jsx)(e.Portal,{children:(0,d.jsx)(e.Content,{ref:h,align:b,sideOffset:c,side:"bottom",className:(0,g.QP)("z-50 origin-(--radix-popover-content-transform-origin) min-w-[240px] max-w-[98vw] rounded-xl border bg-fd-popover/60 backdrop-blur-lg p-2 text-sm text-fd-popover-foreground shadow-lg focus-visible:outline-none data-[state=closed]:animate-fd-popover-out data-[state=open]:animate-fd-popover-in",a),...f})}));j.displayName=e.Content.displayName,e.PopoverClose},98404:(a,b,c)=>{"use strict";c.d(b,{CollapsibleControl:()=>h,LayoutBody:()=>f,Navbar:()=>e,SidebarTrigger:()=>g});var d=c(66352);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs-client.js","Navbar"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call LayoutBody() from the server but LayoutBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs-client.js","LayoutBody"),g=(0,d.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs-client.js","SidebarTrigger"),h=(0,d.registerClientReference)(function(){throw Error("Attempted to call CollapsibleControl() from the server but CollapsibleControl is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs-client.js","CollapsibleControl")},99023:(a,b,c)=>{"use strict";c.d(b,{JotaiProvider:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call JotaiProvider() from the server but JotaiProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/jotai-provider.tsx","JotaiProvider")},99156:(a,b,c)=>{"use strict";c.d(b,{T:()=>e});var d=c(60159);function e(a,b,c=function a(b,c){return Array.isArray(b)&&Array.isArray(c)?c.length!==b.length||b.some((b,d)=>a(b,c[d])):b!==c}){let[f,g]=(0,d.useState)(a);c(f,a)&&(b(a,f),g(a))}}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[657,1770,9119],()=>b(b.s=94638));module.exports=c})();