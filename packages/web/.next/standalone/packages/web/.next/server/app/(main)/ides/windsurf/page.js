"use strict";(()=>{var a={};a.id=1752,a.ids=[1752],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{a.exports=require("util")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},41025:a=>{a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},90480:(a,b,c)=>{c.r(b),c.d(b,{GlobalError:()=>B.default,__next_app__:()=>H,handler:()=>J,pages:()=>G,routeModule:()=>I,tree:()=>F});var d=c(24332),e=c(48819),f=c(93949),g=c(98730),h=c(88996),i=c(16318),j=c(3093),k=c(36748),l=c(98190),m=c(53904),n=c(47735),o=c(20611),p=c(22512),q=c(261),r=c(13863),s=c(8748),t=c(26713),u=c(65262),v=c(97779),w=c(5303),x=c(66704),y=c(67656),z=c(3072),A=c(86439),B=c(866),C=c(97540),D=c(49005),E={};for(let a in C)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(E[a]=()=>C[a]);c.d(b,E);let F={children:["",{children:["(main)",{children:["ides",{children:["windsurf",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,92162)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/ides/windsurf/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,65391)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/ides/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,17217)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,33409)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,92179)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{"global-error":[()=>Promise.resolve().then(c.bind(c,866)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,866)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,50134,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,G=["/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/ides/windsurf/page.tsx"],H={require:c,loadChunk:()=>Promise.resolve()},I=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(main)/ides/windsurf/page",pathname:"/ides/windsurf",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:F},distDir:".next",projectDir:""});async function J(a,b,c){var d;let E="/(main)/ides/windsurf/page";"/index"===E&&(E="/");let K="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await I.prepare(a,b,{srcPage:E,multiZoneDraftMode:K});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(E),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===I.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&I.isDev&&(az=_);let aA={...C,tree:F,pages:G,GlobalError:B.default,handler:J,routeModule:I,__next_app__:H};W&&X&&(0,n.setReferenceManifestsSingleton)({page:E,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return I.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:I,page:E,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:I.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:K,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>I.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:I.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!I.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===I.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await I.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await I.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),I.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!I.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&D.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await I.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},92162:(a,b,c)=>{c.r(b),c.d(b,{default:()=>l,metadata:()=>j});var d=c(38828),e=c(84588),f=c(68366);let g=(0,f.A)("Waves",[["path",{d:"M2 6c.6.5 1.2 1 2.5 1C7 7 7 5 9.5 5c2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"knzxuh"}],["path",{d:"M2 12c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"2jd2cc"}],["path",{d:"M2 18c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"rd2r6e"}]]),h=(0,f.A)("Wind",[["path",{d:"M17.7 7.7a2.5 2.5 0 1 1 1.8 4.3H2",key:"1k4u03"}],["path",{d:"M9.6 4.6A2 2 0 1 1 11 8H2",key:"b7d0fd"}],["path",{d:"M12.6 19.4A2 2 0 1 0 14 16H2",key:"1p5cb3"}]]),i=(0,f.A)("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]),j={title:"Windsurf IDE Integration - AI-Powered Flow Coding",description:"Complete guide to integrating OnlyRules with Windsurf IDE. Learn flow-based AI coding, setup instructions, and optimization tips for enhanced development.",keywords:"Windsurf IDE, AI flow coding, OnlyRules Windsurf, flow-based development, AI IDE integration, Windsurf setup, collaborative coding, AI pair programming",alternates:{canonical:"/ides/windsurf"},openGraph:{title:"Windsurf IDE Integration - OnlyRules Guide",description:"Master flow-based AI coding with Windsurf IDE and OnlyRules. Step-by-step setup and best practices.",images:["/images/windsurf-integration-og.png"]}},k={ide:{name:"Windsurf",icon:"\uD83C\uDF0A",color:"bg-cyan-500",description:"Ride the wave of AI-powered development with flow-based coding",website:"https://codeium.com/windsurf",version:"1.0+"},installation:{steps:[{title:"Download Windsurf",description:"Get Windsurf from the official Codeium website. It's built on VS Code with advanced AI capabilities.",command:"https://codeium.com/windsurf/download",note:"Windsurf is available for all major operating systems and includes Codeium AI built-in."},{title:"Install and Launch",description:"Run the installer and launch Windsurf. The familiar VS Code interface makes it easy to get started."},{title:"Sign In to Codeium",description:"Create or sign in to your Codeium account to activate AI features. Free tier includes generous AI usage.",note:"Windsurf comes with Codeium AI pre-integrated, offering advanced code completion and chat features."},{title:"Configure AI Settings",description:"Access the Windsurf AI settings to customize your coding experience and model preferences.",command:"Cmd/Ctrl + Shift + P → Windsurf: AI Settings"}]},integration:{steps:[{title:"Browse OnlyRules Templates",description:"Visit OnlyRules and filter for Windsurf-compatible prompt rules and templates.",code:"https://onlyrules.app/templates?ide=WINDSURF"},{title:"Create Windsurf Rules",description:"Set up a .windsurf directory in your project with a rules.md file for custom prompts.",code:`mkdir .windsurf
touch .windsurf/rules.md`},{title:"Configure Flow Rules",description:"Add your OnlyRules prompts to enable flow-based coding patterns in Windsurf.",code:`# .windsurf/rules.md

## Flow-Based Development Rules

### Component Flow
When creating new components:
1. Start with interface definitions
2. Implement core logic
3. Add error handling
4. Include comprehensive tests

### AI Collaboration Flow
- Use cascading prompts for complex features
- Break down tasks into atomic operations
- Maintain context between AI interactions

### Code Quality Standards
- Follow functional programming principles
- Implement proper error boundaries
- Use TypeScript strictly
- Document complex logic flows`},{title:"Activate Flow Mode",description:"Use Windsurf's unique flow mode to chain AI operations and build features incrementally.",code:"Ctrl/Cmd + Shift + F → Start Flow Mode"}]},features:[{title:"Flow-Based Coding",description:"Chain AI operations together for complex multi-step development tasks",icon:"waves"},{title:"Multi-Agent Collaboration",description:"Multiple AI agents working together on different aspects of your code",icon:"wind"},{title:"Cloud Sync",description:"Sync your flows and rules across devices with cloud integration",icon:"cloud"}],examples:[{title:"Multi-File Feature Implementation",description:"Build a complete feature across multiple files using flow mode",prompt:`Flow: Create user authentication system
Step 1: Design database schema for users
Step 2: Create API endpoints for login/register
Step 3: Build React components for auth forms
Step 4: Implement JWT token handling
Step 5: Add middleware for protected routes
Step 6: Create unit and integration tests`,result:"Complete authentication system with all components properly integrated"},{title:"Cascading Refactor",description:"Refactor code across your entire codebase with intelligent flow",prompt:`Flow: Migrate from Redux to Zustand
- Analyze current Redux implementation
- Create equivalent Zustand stores
- Update all component connections
- Migrate middleware and side effects
- Update tests to match new state management
- Remove Redux dependencies`,result:"Seamless migration with all components updated and tested"},{title:"AI-Driven Code Review",description:"Perform comprehensive code review with actionable improvements",prompt:`Flow: Review and improve codebase
1. Analyze code for performance bottlenecks
2. Identify security vulnerabilities
3. Suggest architectural improvements
4. Implement critical fixes
5. Add missing documentation
6. Optimize bundle size`,result:"Detailed review with implemented improvements and documentation"}],tips:["Use Windsurf's flow mode to break complex tasks into manageable AI-assisted steps","Leverage the multi-agent system by assigning different aspects of a feature to different agents","Create reusable flow templates for common development patterns in your team","Use the cascade feature to propagate changes intelligently across related files",'Enable Windsurf\'s "Context Preservation" to maintain AI understanding across sessions',"Combine OnlyRules templates with Windsurf flows for maximum productivity","Use the built-in collaboration features to share flows with team members","Set up project-specific flows that align with your architecture patterns","Take advantage of Windsurf's VS Code heritage by using familiar extensions","Regularly sync your flows to the cloud for backup and cross-device access"]};function l(){let a={waves:(0,d.jsx)(g,{className:"h-5 w-5"}),wind:(0,d.jsx)(h,{className:"h-5 w-5"}),cloud:(0,d.jsx)(i,{className:"h-5 w-5"})},b=k.features.map(b=>({...b,icon:a[b.icon]}));return(0,d.jsx)(e.default,{...k,features:b})}}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[657,5208,1770,9119,1985,3931,3933],()=>b(b.s=90480));module.exports=c})();