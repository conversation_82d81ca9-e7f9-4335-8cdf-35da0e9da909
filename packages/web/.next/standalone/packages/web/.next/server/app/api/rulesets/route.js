(()=>{var a={};a.id=2691,a.ids=[2691],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:a=>{"use strict";a.exports=require("node:buffer")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26316:(a,b,c)=>{"use strict";c.d(b,{z:()=>e});var d=c(96330);let e=globalThis.prisma??new d.PrismaClient},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31316:(a,b,c)=>{"use strict";c.d(b,{a:()=>n,j:()=>m});var d=c(18529),e=c(12306),f=c(96330);let g=null,h=!process.env.VERCEL&&!process.env.RAILWAY_ENVIRONMENT,i=process.env.DATABASE_URL?.includes("build-mock-host")||process.env.DATABASE_URL?.includes("localhost"),j=process.env.DATABASE_URL&&process.env.DATABASE_URL.startsWith("postgresql://")&&!i;if(!h&&j)try{g=new f.PrismaClient}catch(a){console.warn("Failed to create Prisma client:",a)}let k=null;function l(){if(!k){let a={secret:process.env.BETTER_AUTH_SECRET||"default-secret-change-in-production",baseURL:"https://onlyrules.codes",trustedOrigins:["http://localhost:3000","https://onlyrules.codes"],emailAndPassword:{enabled:!1},session:{expiresIn:604800,updateAge:86400},socialProviders:{...process.env.GITHUB_CLIENT_ID&&process.env.GITHUB_CLIENT_SECRET?{github:{clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET,scope:["read:user","user:email"]}}:{}},callbacks:{async signIn({user:a,account:b,profile:c}){if(!a.email&&b?.provider==="github")if(console.log("GitHub user without email:",{user:a,profile:c}),c&&c.email)a.email=c.email;else{let b=a.name?.toLowerCase().replace(/[^a-z0-9]/g,".")||"user";a.email=`${b}.${Date.now()}@github.user`,console.log("Created placeholder email:",a.email)}return!0}}};g&&(a.database=(0,e._)(g,{provider:"postgresql"})),k=(0,d.li)(a)}return k}let m=new Proxy({},{get:(a,b)=>l()[b]}),n=()=>l()},37067:a=>{"use strict";a.exports=require("node:http")},44708:a=>{"use strict";a.exports=require("node:https")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55081:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>G,patchFetch:()=>F,routeModule:()=>B,serverHooks:()=>E,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>D});var d={};c.r(d),c.d(d,{GET:()=>z,POST:()=>A,dynamic:()=>y});var e=c(48106),f=c(48819),g=c(12050),h=c(88996),i=c(98730),j=c(261),k=c(36748),l=c(27462),m=c(16318),n=c(93949),o=c(36523),p=c(45393),q=c(41671),r=c(66704),s=c(86439),t=c(65262),u=c(4235),v=c(26316),w=c(31316),x=c(65208);let y="force-dynamic";async function z(a){if(!process.env.VERCEL&&!process.env.RAILWAY_ENVIRONMENT)return u.NextResponse.json([]);try{let b=await w.j.api.getSession({headers:await (0,x.b3)()}),{searchParams:c}=new URL(a.url),d=c.get("search")||"",e=c.get("visibility"),f={OR:[{visibility:"PUBLIC"},b?.user?{userId:b.user.id}:{}]};d&&(f.OR=[{name:{contains:d,mode:"insensitive"}},{description:{contains:d,mode:"insensitive"}}]),e&&(f.visibility=e);let g=await v.z.ruleset.findMany({where:f,include:{rules:{include:{rule:{include:{tags:{include:{tag:!0}},user:{select:{id:!0,name:!0,email:!0}}}}},orderBy:{order:"asc"}},user:{select:{id:!0,name:!0,email:!0}}},orderBy:{updatedAt:"desc"}});return u.NextResponse.json(g)}catch(a){return console.error("Error fetching rulesets:",a),u.NextResponse.json({error:"Failed to fetch rulesets"},{status:500})}}async function A(a){try{let b=await w.j.api.getSession({headers:await (0,x.b3)()});if(!b?.user)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{name:c,description:d,visibility:e,ruleIds:f}=await a.json();if(!c||!c.trim())return u.NextResponse.json({error:"Ruleset name is required"},{status:400});let g=await v.z.ruleset.create({data:{name:c.trim(),description:d?.trim()||null,visibility:e||"PRIVATE",userId:b.user.id,shareToken:"PUBLIC"===e?crypto.randomUUID():null}});if(f&&Array.isArray(f)&&f.length>0){if((await v.z.rule.findMany({where:{id:{in:f},OR:[{userId:b.user.id},{visibility:"PUBLIC"}]}})).length!==f.length)return await v.z.ruleset.delete({where:{id:g.id}}),u.NextResponse.json({error:"Some rules not found or access denied"},{status:400});await Promise.all(f.map((a,b)=>v.z.rulesetRule.create({data:{rulesetId:g.id,ruleId:a,order:b}})))}let h=await v.z.ruleset.findUnique({where:{id:g.id},include:{rules:{include:{rule:{include:{tags:{include:{tag:!0}},user:{select:{id:!0,name:!0,email:!0}}}}},orderBy:{order:"asc"}},user:{select:{id:!0,name:!0,email:!0}}}});return u.NextResponse.json(h)}catch(a){return console.error("Error creating ruleset:",a),u.NextResponse.json({error:"Failed to create ruleset"},{status:500})}}let B=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/rulesets/route",pathname:"/api/rulesets",filename:"route",bundlePath:"app/api/rulesets/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/api/rulesets/route.ts",nextConfigOutput:"standalone",userland:d}),{workAsyncStorage:C,workUnitAsyncStorage:D,serverHooks:E}=B;function F(){return(0,g.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:D})}async function G(a,b,c){var d;let e="/api/rulesets/route";"/index"===e&&(e="/");let g=await B.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||B.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===B.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>B.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>B.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await B.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await B.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await B.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},57975:a=>{"use strict";a.exports=require("node:util")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77598:a=>{"use strict";a.exports=require("node:crypto")},78474:a=>{"use strict";a.exports=require("node:events")},80408:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87032:()=>{},96330:a=>{"use strict";a.exports=require("@prisma/client")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[657,5208,2626,3608],()=>b(b.s=55081));module.exports=c})();