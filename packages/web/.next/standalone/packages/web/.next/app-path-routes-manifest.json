{"/_not-found/page": "/_not-found", "/api/search/route": "/api/search", "/api/robots.txt/route": "/api/robots.txt", "/api/auth/[...all]/route": "/api/auth/[...all]", "/api/rulesets/[id]/route": "/api/rulesets/[id]", "/api/rules/raw/route": "/api/rules/raw", "/api/rules/[id]/route": "/api/rules/[id]", "/api/rules/export/route": "/api/rules/export", "/sitemap.xml/route": "/sitemap.xml", "/api/rulesets/route": "/api/rulesets", "/api/tags/route": "/api/tags", "/api/sitemap.xml/route": "/api/sitemap.xml", "/api/rules/download/route": "/api/rules/download", "/api/rules/route": "/api/rules", "/(main)/r/[id]/page": "/r/[id]", "/(main)/test-theme/page": "/test-theme", "/(main)/test-integration/page": "/test-integration", "/(main)/auth/signin/page": "/auth/signin", "/(main)/auth/signup/page": "/auth/signup", "/(main)/profile/[username]/page": "/profile/[username]", "/(main)/rulesets/page": "/rulesets", "/(main)/rulesets/[id]/page": "/rulesets/[id]", "/(main)/dashboard/page": "/dashboard", "/(main)/tutorials/page": "/tutorials", "/(main)/rules/[id]/page": "/rules/[id]", "/(main)/templates/page": "/templates", "/(docs)/docs/[[...slug]]/page": "/docs/[[...slug]]", "/(main)/ides/github-copilot/page": "/ides/github-copilot", "/(main)/ides/page": "/ides", "/(main)/ides/cline/page": "/ides/cline", "/(main)/ides/claude/page": "/ides/claude", "/(main)/ides/windsurf/page": "/ides/windsurf", "/(main)/ides/cursor/page": "/ides/cursor", "/(main)/ides/augment/page": "/ides/augment", "/(main)/(static)/privacy/page": "/privacy", "/(main)/(static)/page": "/", "/(main)/(static)/terms/page": "/terms"}